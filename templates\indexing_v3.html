{% extends "layout_v3.html" %}

{% block title %}Indexing v3.0 - TextFinder{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 style="font-size: 2.5rem; font-weight: 700; color: var(--primary-color); margin-bottom: 1rem;">
                <i class="fas fa-database"></i> File Indexing
                <span style="font-size: 1rem; color: var(--secondary-color); font-weight: 400;">(v3.0 - Fresh Design)</span>
            </h1>
            <p style="font-size: 1.125rem; color: var(--secondary-color); margin-bottom: 2rem;">
                Index multiple folders for faster searching across your document collection with our modern interface.
            </p>
            
            <!-- Success Notice -->
            <div id="fresh-design-notice" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 1rem 1.5rem; border-radius: var(--border-radius); margin-bottom: 2rem; display: flex; align-items: center; gap: 1rem;">
                <i class="fas fa-check-circle" style="font-size: 1.5rem;"></i>
                <div>
                    <strong>Fresh Design Loaded!</strong> You're now using the completely redesigned TextFinder v3.0 indexing interface with no caching issues.
                    <button onclick="this.parentElement.parentElement.style.display='none'" style="background: none; border: none; color: white; float: right; cursor: pointer; font-size: 1.2rem;">×</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Index Statistics Dashboard -->
    <div class="row mb-4">
        <div class="col-3">
            <div class="card" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%); color: white; text-align: center;">
                <div class="card-body">
                    <i class="fas fa-folder" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.8;"></i>
                    <h2 id="total-folders" style="margin: 0; font-size: 2.5rem; font-weight: 700;">0</h2>
                    <p style="margin: 0; opacity: 0.9;">Indexed Folders</p>
                </div>
            </div>
        </div>
        <div class="col-3">
            <div class="card" style="background: linear-gradient(135deg, var(--success-color) 0%, #047857 100%); color: white; text-align: center;">
                <div class="card-body">
                    <i class="fas fa-file" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.8;"></i>
                    <h2 id="total-files" style="margin: 0; font-size: 2.5rem; font-weight: 700;">0</h2>
                    <p style="margin: 0; opacity: 0.9;">Total Files</p>
                </div>
            </div>
        </div>
        <div class="col-3">
            <div class="card" style="background: linear-gradient(135deg, var(--info-color) 0%, #0e7490 100%); color: white; text-align: center;">
                <div class="card-body">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.8;"></i>
                    <h2 id="indexed-files" style="margin: 0; font-size: 2.5rem; font-weight: 700;">0</h2>
                    <p style="margin: 0; opacity: 0.9;">Indexed Files</p>
                </div>
            </div>
        </div>
        <div class="col-3">
            <div class="card" style="background: linear-gradient(135deg, var(--warning-color) 0%, #c2410c 100%); color: white; text-align: center;">
                <div class="card-body">
                    <i class="fas fa-cog" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.8;"></i>
                    <h2 id="index-status" style="margin: 0; font-size: 1.5rem; font-weight: 700;">Idle</h2>
                    <p style="margin: 0; opacity: 0.9;">Status</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Folder Management Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-folder-plus" style="color: var(--primary-color);"></i>
                        Folder Management
                    </h3>
                    <div style="display: flex; gap: 0.5rem;">
                        <button id="refresh-folders" class="btn btn-outline btn-sm">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button id="clear-index" class="btn btn-danger btn-sm">
                            <i class="fas fa-trash"></i> Clear Index
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Add New Folder -->
                    <div class="row mb-4">
                        <div class="col-8">
                            <div style="display: flex; gap: 0.5rem;">
                                <div style="flex: 1; position: relative;">
                                    <i class="fas fa-folder" style="position: absolute; left: 1rem; top: 50%; transform: translateY(-50%); color: var(--secondary-color);"></i>
                                    <input type="text" id="new-folder-input" class="form-control" placeholder="Enter folder path or browse..." readonly style="padding-left: 2.5rem;">
                                </div>
                                <button id="browse-folder-btn" class="btn btn-primary">
                                    <i class="fas fa-folder-open"></i> Browse
                                </button>
                            </div>
                        </div>
                        <div class="col-4">
                            <button id="add-folder-btn" class="btn btn-success w-full" disabled>
                                <i class="fas fa-plus"></i> Add to Index
                            </button>
                        </div>
                    </div>

                    <!-- Indexed Folders List -->
                    <div class="mb-3">
                        <h4 style="margin-bottom: 1rem; color: var(--primary-color);">Indexed Folders</h4>
                        <div id="indexed-folders-container">
                            <div id="no-folders-message" style="text-align: center; padding: 2rem; background: var(--light-color); border-radius: var(--border-radius); border: 2px dashed var(--border-color);">
                                <i class="fas fa-info-circle" style="font-size: 2rem; color: var(--secondary-color); margin-bottom: 1rem;"></i>
                                <p style="color: var(--secondary-color); margin: 0;">No folders indexed yet. Add folders above to begin.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Interface Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-search" style="color: var(--primary-color);"></i>
                        Search Indexed Folders
                    </h3>
                </div>
                <div class="card-body">
                    <form id="indexed-search-form">
                        <div class="row mb-3">
                            <div class="col-8">
                                <label for="indexed-search-query" class="form-label">Search Query</label>
                                <input type="text" id="indexed-search-query" class="form-control" placeholder="Enter text to search for..." required>
                            </div>
                            <div class="col-4" style="display: flex; align-items: end;">
                                <button type="submit" id="indexed-search-btn" class="btn btn-primary btn-lg w-full" disabled>
                                    <i class="fas fa-search"></i> Search Index
                                </button>
                            </div>
                        </div>
                        
                        <!-- Folder Filter -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label" style="font-weight: 600; margin-bottom: 1rem;">Filter by Folders:</label>
                                <div id="folder-filters" style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                    <span style="color: var(--secondary-color); font-style: italic;">No indexed folders available</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Document Type Filters -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label" style="font-weight: 600; margin-bottom: 1rem;">Document Type Filters:</label>
                                <div id="indexed-doc-type-filters" style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                    <label class="filter-toggle" data-type="pdf">
                                        <input type="checkbox" checked style="display: none;">
                                        <span class="filter-btn" style="background: #dc2626; color: white;">
                                            <i class="fas fa-file-pdf"></i> PDF
                                        </span>
                                    </label>
                                    <label class="filter-toggle" data-type="word">
                                        <input type="checkbox" checked style="display: none;">
                                        <span class="filter-btn" style="background: #2563eb; color: white;">
                                            <i class="fas fa-file-word"></i> Word
                                        </span>
                                    </label>
                                    <label class="filter-toggle" data-type="excel">
                                        <input type="checkbox" checked style="display: none;">
                                        <span class="filter-btn" style="background: #059669; color: white;">
                                            <i class="fas fa-file-excel"></i> Excel
                                        </span>
                                    </label>
                                    <label class="filter-toggle" data-type="powerpoint">
                                        <input type="checkbox" checked style="display: none;">
                                        <span class="filter-btn" style="background: #d97706; color: white;">
                                            <i class="fas fa-file-powerpoint"></i> PowerPoint
                                        </span>
                                    </label>
                                    <label class="filter-toggle" data-type="images">
                                        <input type="checkbox" checked style="display: none;">
                                        <span class="filter-btn" style="background: #0891b2; color: white;">
                                            <i class="fas fa-file-image"></i> Images
                                        </span>
                                    </label>
                                    <label class="filter-toggle" data-type="text">
                                        <input type="checkbox" checked style="display: none;">
                                        <span class="filter-btn" style="background: #64748b; color: white;">
                                            <i class="fas fa-file-alt"></i> Text
                                        </span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Search Options -->
                        <div class="row">
                            <div class="col-12">
                                <button type="button" id="toggle-indexed-advanced" class="btn btn-outline btn-sm mb-3">
                                    <i class="fas fa-cog"></i> Advanced Options
                                    <i class="fas fa-chevron-down" id="indexed-advanced-chevron"></i>
                                </button>
                                
                                <div id="indexed-advanced-options" style="display: none; background: var(--light-color); padding: 1.5rem; border-radius: var(--border-radius); border: 1px solid var(--border-color);">
                                    <div class="row">
                                        <div class="col-6">
                                            <h4 style="margin-bottom: 1rem; color: var(--primary-color);">Search Options</h4>
                                            <div class="form-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" id="indexed-case-sensitive">
                                                    <span class="checkmark"></span>
                                                    Case Sensitive
                                                </label>
                                            </div>
                                            <div class="form-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" id="indexed-whole-word">
                                                    <span class="checkmark"></span>
                                                    Whole Word Only
                                                </label>
                                            </div>
                                            <div class="form-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" id="indexed-use-regex">
                                                    <span class="checkmark"></span>
                                                    Regular Expression
                                                </label>
                                            </div>
                                            <div class="form-group">
                                                <label for="indexed-search-mode" class="form-label">Multiple Keywords</label>
                                                <select id="indexed-search-mode" class="form-control form-select">
                                                    <option value="AND">All keywords (AND)</option>
                                                    <option value="OR">Any keyword (OR)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <h4 style="margin-bottom: 1rem; color: var(--primary-color);">Index Management</h4>
                                            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                                                <button type="button" id="reindex-all-btn" class="btn btn-outline">
                                                    <i class="fas fa-sync"></i> Re-index All Folders
                                                </button>
                                                <button type="button" id="rebuild-index-btn" class="btn btn-warning">
                                                    <i class="fas fa-hammer"></i> Rebuild Index
                                                </button>
                                                <button type="button" id="optimize-index-btn" class="btn btn-outline">
                                                    <i class="fas fa-compress-arrows-alt"></i> Optimize Index
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Indexing Progress -->
    <div id="indexing-progress" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card" style="border-left: 4px solid var(--info-color);">
                <div class="card-header" style="background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%); color: white;">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-spinner fa-spin"></i> Indexing in Progress
                    </h3>
                </div>
                <div class="card-body">
                    <div class="progress-container" style="background: var(--light-color); border-radius: var(--border-radius); overflow: hidden; margin-bottom: 1rem;">
                        <div id="indexing-progress-bar" style="height: 8px; background: linear-gradient(90deg, var(--primary-color) 0%, var(--info-color) 100%); width: 0%; transition: width 0.3s ease;"></div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <p style="margin-bottom: 0.5rem;"><strong>Current Folder:</strong> <span id="current-indexing-folder">-</span></p>
                            <p style="margin-bottom: 0.5rem;"><strong>Current File:</strong> <span id="current-indexing-file">-</span></p>
                        </div>
                        <div class="col-6">
                            <p style="margin-bottom: 0.5rem;"><strong>Progress:</strong> <span id="indexing-file-progress">0 / 0</span></p>
                            <p style="margin-bottom: 0.5rem;"><strong>Status:</strong> <span id="indexing-status">Initializing...</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Progress -->
    <div id="indexed-search-progress" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card" style="border-left: 4px solid var(--warning-color);">
                <div class="card-header" style="background: linear-gradient(135deg, var(--warning-color) 0%, #c2410c 100%); color: white;">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-spinner fa-spin"></i> Searching Index
                    </h3>
                </div>
                <div class="card-body">
                    <div class="progress-container" style="background: var(--light-color); border-radius: var(--border-radius); overflow: hidden; margin-bottom: 1rem;">
                        <div id="indexed-search-progress-bar" style="height: 8px; background: linear-gradient(90deg, var(--warning-color) 0%, var(--primary-color) 100%); width: 0%; transition: width 0.3s ease;"></div>
                    </div>
                    <div style="text-align: center;">
                        <p style="margin-bottom: 0.5rem;"><strong>Status:</strong> <span id="indexed-search-status">Searching...</span></p>
                        <p style="margin-bottom: 0.5rem;"><strong>Matches Found:</strong> <span id="indexed-matches-found">0</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Results -->
    <div id="indexed-search-results" class="row" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-list" style="color: var(--success-color);"></i>
                        Search Results (<span id="indexed-results-count">0</span> files)
                    </h3>
                    <div style="display: flex; gap: 0.5rem;">
                        <button id="expand-all-indexed-results" class="btn btn-outline btn-sm">
                            <i class="fas fa-expand"></i> Expand All
                        </button>
                        <button id="collapse-all-indexed-results" class="btn btn-outline btn-sm">
                            <i class="fas fa-compress"></i> Collapse All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="indexed-results-container">
                        <!-- Results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Folder Browser Modal -->
<div id="folder-browser-modal" class="modal" style="display: none;">
    <div class="modal-backdrop" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 1000;"></div>
    <div class="modal-dialog" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 90%; max-width: 800px; z-index: 1001;">
        <div class="modal-content" style="background: white; border-radius: var(--border-radius); box-shadow: var(--shadow-lg); overflow: hidden;">
            <div class="modal-header" style="padding: 1.5rem; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-folder-open" style="color: var(--primary-color);"></i>
                    Browse Folder
                </h3>
                <button id="close-folder-modal" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--secondary-color);">×</button>
            </div>
            <div class="modal-body" style="padding: 1.5rem;">
                <div class="mb-3">
                    <nav id="folder-breadcrumb" style="background: var(--light-color); padding: 0.75rem; border-radius: var(--border-radius); font-size: 0.875rem;">
                        Loading...
                    </nav>
                </div>
                <div id="folder-contents" style="max-height: 400px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                    <div style="text-align: center; padding: 2rem;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--secondary-color);"></i>
                        <p style="margin-top: 1rem; color: var(--secondary-color);">Loading...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer" style="padding: 1.5rem; border-top: 1px solid var(--border-color); display: flex; justify-content: space-between;">
                <button id="cancel-folder-modal" class="btn btn-secondary">Cancel</button>
                <button id="select-folder-btn" class="btn btn-primary" disabled>Select Folder</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Include CSS Styles (reuse from Direct Search) -->
{% include 'direct_search_v3_styles.html' %}

<!-- Include JavaScript Functionality -->
{% include 'indexing_v3_scripts.html' %}
{% include 'indexing_v3_scripts_part2.html' %}
{% include 'indexing_v3_management.html' %}
{% endblock %}
