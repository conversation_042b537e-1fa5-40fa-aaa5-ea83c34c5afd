{% extends "layout.html" %}

{% block title %}TextFinder - Direct Search{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-search"></i> Direct File Search
            </h1>
            <p class="lead">Search for text in files without indexing. Select files or folders to search through.</p>
        </div>
    </div>

    <!-- File Selection Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-folder-open"></i> File Selection
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="select-all-files" disabled>
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="clear-selection" disabled>
                            <i class="fas fa-square"></i> Clear All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-folder"></i></span>
                                <input type="text" class="form-control" id="directory-input" placeholder="Enter directory path or browse..." readonly>
                                <button type="button" class="btn btn-primary" id="browse-directory-btn">
                                    <i class="fas fa-folder-open"></i> Browse
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-success w-100" id="scan-directory-btn" disabled>
                                <i class="fas fa-search"></i> Scan Directory
                            </button>
                        </div>
                    </div>
                    
                    <!-- Document Type Filters -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <label class="form-label fw-bold">Document Type Filters:</label>
                            <div class="btn-group flex-wrap" role="group" id="doc-type-filters">
                                <input type="checkbox" class="btn-check" id="filter-pdf" checked>
                                <label class="btn btn-outline-danger" for="filter-pdf">
                                    <i class="fas fa-file-pdf"></i> PDF
                                </label>
                                
                                <input type="checkbox" class="btn-check" id="filter-word" checked>
                                <label class="btn btn-outline-primary" for="filter-word">
                                    <i class="fas fa-file-word"></i> Word
                                </label>
                                
                                <input type="checkbox" class="btn-check" id="filter-excel" checked>
                                <label class="btn btn-outline-success" for="filter-excel">
                                    <i class="fas fa-file-excel"></i> Excel
                                </label>
                                
                                <input type="checkbox" class="btn-check" id="filter-powerpoint" checked>
                                <label class="btn btn-outline-warning" for="filter-powerpoint">
                                    <i class="fas fa-file-powerpoint"></i> PowerPoint
                                </label>
                                
                                <input type="checkbox" class="btn-check" id="filter-images" checked>
                                <label class="btn btn-outline-info" for="filter-images">
                                    <i class="fas fa-file-image"></i> Images
                                </label>
                                
                                <input type="checkbox" class="btn-check" id="filter-text" checked>
                                <label class="btn btn-outline-secondary" for="filter-text">
                                    <i class="fas fa-file-alt"></i> Text
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- File List -->
                    <div id="file-list-container">
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle"></i> Select a directory and click "Scan Directory" to browse files.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i> Search Options
                    </h5>
                </div>
                <div class="card-body">
                    <form id="search-form">
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="search-query" class="form-label">Search Query</label>
                                <input type="text" class="form-control" id="search-query" placeholder="Enter text to search for..." required>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100" id="search-btn" disabled>
                                    <i class="fas fa-search"></i> Search Files
                                </button>
                            </div>
                        </div>
                        
                        <!-- Advanced Search Options -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-outline-secondary btn-sm mb-3" type="button" data-bs-toggle="collapse" data-bs-target="#advanced-options" aria-expanded="false">
                                    <i class="fas fa-cog"></i> Advanced Options
                                </button>
                                
                                <div class="collapse" id="advanced-options">
                                    <div class="card card-body bg-light">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6 class="fw-bold">Search Options</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="case-sensitive">
                                                    <label class="form-check-label" for="case-sensitive">
                                                        Case Sensitive
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="whole-word">
                                                    <label class="form-check-label" for="whole-word">
                                                        Whole Word Only
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="use-regex">
                                                    <label class="form-check-label" for="use-regex">
                                                        Regular Expression
                                                    </label>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="search-mode" class="form-label">Multiple Keywords</label>
                                                    <select class="form-select" id="search-mode">
                                                        <option value="AND">All keywords (AND)</option>
                                                        <option value="OR">Any keyword (OR)</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="fw-bold">OCR Options</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="enable-ocr" checked>
                                                    <label class="form-check-label" for="enable-ocr">
                                                        Enable OCR for Images/PDFs
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="handwriting-recognition">
                                                    <label class="form-check-label" for="handwriting-recognition">
                                                        Handwriting Recognition
                                                    </label>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="ocr-language" class="form-label">OCR Language</label>
                                                    <select class="form-select" id="ocr-language">
                                                        <option value="eng">English</option>
                                                        <option value="spa">Spanish</option>
                                                        <option value="fra">French</option>
                                                        <option value="deu">German</option>
                                                        <option value="chi_sim">Chinese (Simplified)</option>
                                                    </select>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="ocr-dpi" class="form-label">OCR DPI</label>
                                                    <select class="form-select" id="ocr-dpi">
                                                        <option value="150">150 DPI (Fast)</option>
                                                        <option value="300" selected>300 DPI (Standard)</option>
                                                        <option value="600">600 DPI (High Quality)</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Image Preprocessing Options -->
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <button class="btn btn-outline-info btn-sm mb-2" type="button" data-bs-toggle="collapse" data-bs-target="#preprocessing-options">
                                                    <i class="fas fa-image"></i> Image Preprocessing Options
                                                </button>
                                                <div class="collapse" id="preprocessing-options">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="bilateral-filter">
                                                                <label class="form-check-label" for="bilateral-filter">
                                                                    Bilateral Filter
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="gaussian-blur">
                                                                <label class="form-check-label" for="gaussian-blur">
                                                                    Gaussian Blur
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="contrast-enhancement">
                                                                <label class="form-check-label" for="contrast-enhancement">
                                                                    Contrast Enhancement
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Progress -->
    <div id="search-progress" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-spinner fa-spin"></i> Searching Files
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" id="search-progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Current File:</strong> <span id="current-file">-</span></p>
                            <p class="mb-1"><strong>Progress:</strong> <span id="file-progress">0 / 0</span></p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Matches Found:</strong> <span id="matches-found">0</span></p>
                            <p class="mb-1"><strong>Status:</strong> <span id="search-status">Initializing...</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Results -->
    <div id="search-results" class="row" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> Search Results (<span id="results-count">0</span> files)
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="expand-all-results">
                            <i class="fas fa-expand"></i> Expand All
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="collapse-all-results">
                            <i class="fas fa-compress"></i> Collapse All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="results-container">
                        <!-- Results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PDF Creation Section -->
    <div id="pdf-creation" class="row mt-4" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-pdf"></i> Create Searchable PDFs
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-warning w-100 mb-2" id="create-single-pdf">
                                <i class="fas fa-file-pdf"></i> Create Searchable PDF (Selected File)
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-danger w-100 mb-2" id="create-batch-pdf">
                                <i class="fas fa-files-o"></i> Batch Create Searchable PDFs
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Directory Browser Modal -->
<div class="modal fade" id="directory-browser-modal" tabindex="-1" aria-labelledby="directoryBrowserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="directoryBrowserModalLabel">
                    <i class="fas fa-folder-open"></i> Browse Directory
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb" id="directory-breadcrumb">
                            <li class="breadcrumb-item active">Loading...</li>
                        </ol>
                    </nav>
                </div>
                <div id="directory-contents" class="list-group">
                    <div class="text-center p-3">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="select-directory-btn" disabled>Select Directory</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let selectedDirectory = '';
    let scannedFiles = [];
    let selectedFiles = [];
    let searchInProgress = false;
    let currentSearchResults = [];

    // DOM elements
    const directoryInput = document.getElementById('directory-input');
    const browseDirectoryBtn = document.getElementById('browse-directory-btn');
    const scanDirectoryBtn = document.getElementById('scan-directory-btn');
    const selectAllFilesBtn = document.getElementById('select-all-files');
    const clearSelectionBtn = document.getElementById('clear-selection');
    const fileListContainer = document.getElementById('file-list-container');
    const searchForm = document.getElementById('search-form');
    const searchBtn = document.getElementById('search-btn');
    const searchQuery = document.getElementById('search-query');
    const searchProgress = document.getElementById('search-progress');
    const searchResults = document.getElementById('search-results');
    const directoryBrowserModal = new bootstrap.Modal(document.getElementById('directory-browser-modal'));

    // Document type filters
    const docTypeFilters = {
        pdf: document.getElementById('filter-pdf'),
        word: document.getElementById('filter-word'),
        excel: document.getElementById('filter-excel'),
        powerpoint: document.getElementById('filter-powerpoint'),
        images: document.getElementById('filter-images'),
        text: document.getElementById('filter-text')
    };

    // File type mappings
    const fileTypeMap = {
        pdf: ['pdf'],
        word: ['doc', 'docx'],
        excel: ['xls', 'xlsx', 'csv'],
        powerpoint: ['ppt', 'pptx'],
        images: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif'],
        text: ['txt', 'md', 'log', 'json', 'xml', 'html', 'css', 'js', 'py', 'java', 'cpp', 'c', 'h']
    };

    // Event listeners
    browseDirectoryBtn.addEventListener('click', openDirectoryBrowser);
    scanDirectoryBtn.addEventListener('click', scanDirectory);
    selectAllFilesBtn.addEventListener('click', selectAllFiles);
    clearSelectionBtn.addEventListener('click', clearSelection);
    searchForm.addEventListener('submit', performSearch);

    // Document type filter listeners
    Object.values(docTypeFilters).forEach(filter => {
        filter.addEventListener('change', applyDocumentTypeFilters);
    });

    // Directory browser functionality
    function openDirectoryBrowser() {
        directoryBrowserModal.show();
        loadDirectoryContents('/');
    }

    function loadDirectoryContents(path) {
        const breadcrumb = document.getElementById('directory-breadcrumb');
        const contents = document.getElementById('directory-contents');

        // Update breadcrumb
        updateBreadcrumb(path);

        // Show loading
        contents.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading directory contents...</p>
            </div>
        `;

        // Make AJAX request to browse directory
        fetch('/browse-directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ path: path })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                displayDirectoryContents(data.contents, path);
            } else {
                contents.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading directory:', error);
            contents.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error loading directory contents.
                </div>
            `;
        });
    }

    function updateBreadcrumb(path) {
        const breadcrumb = document.getElementById('directory-breadcrumb');
        const parts = path.split('/').filter(part => part);

        let breadcrumbHTML = '<li class="breadcrumb-item"><a href="#" data-path="/">Root</a></li>';
        let currentPath = '';

        parts.forEach((part, index) => {
            currentPath += '/' + part;
            if (index === parts.length - 1) {
                breadcrumbHTML += `<li class="breadcrumb-item active">${part}</li>`;
            } else {
                breadcrumbHTML += `<li class="breadcrumb-item"><a href="#" data-path="${currentPath}">${part}</a></li>`;
            }
        });

        breadcrumb.innerHTML = breadcrumbHTML;

        // Add click listeners to breadcrumb links
        breadcrumb.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                loadDirectoryContents(link.dataset.path);
            });
        });
    }

    function displayDirectoryContents(contents, currentPath) {
        const contentsDiv = document.getElementById('directory-contents');
        const selectBtn = document.getElementById('select-directory-btn');

        if (!contents || contents.length === 0) {
            contentsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> This directory is empty.
                </div>
            `;
            selectBtn.disabled = false;
            selectBtn.onclick = () => selectDirectory(currentPath);
            return;
        }

        let html = '';

        // Add parent directory link if not at root
        if (currentPath !== '/') {
            const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
            html += `
                <a href="#" class="list-group-item list-group-item-action" data-path="${parentPath}">
                    <i class="fas fa-level-up-alt text-secondary"></i> .. (Parent Directory)
                </a>
            `;
        }

        // Sort contents: directories first, then files
        contents.sort((a, b) => {
            if (a.type === 'directory' && b.type !== 'directory') return -1;
            if (a.type !== 'directory' && b.type === 'directory') return 1;
            return a.name.localeCompare(b.name);
        });

        contents.forEach(item => {
            const icon = item.type === 'directory' ? 'fas fa-folder text-warning' : 'fas fa-file text-secondary';
            const itemPath = currentPath === '/' ? `/${item.name}` : `${currentPath}/${item.name}`;

            html += `
                <a href="#" class="list-group-item list-group-item-action" data-path="${itemPath}" data-type="${item.type}">
                    <i class="${icon}"></i> ${item.name}
                    ${item.type === 'file' ? `<small class="text-muted">(${formatFileSize(item.size || 0)})</small>` : ''}
                </a>
            `;
        });

        contentsDiv.innerHTML = html;

        // Add click listeners
        contentsDiv.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                if (link.dataset.type === 'directory') {
                    loadDirectoryContents(link.dataset.path);
                }
            });
        });

        // Enable select button
        selectBtn.disabled = false;
        selectBtn.onclick = () => selectDirectory(currentPath);
    }

    function selectDirectory(path) {
        selectedDirectory = path;
        directoryInput.value = path;
        scanDirectoryBtn.disabled = false;
        directoryBrowserModal.hide();

        // Clear previous file list
        fileListContainer.innerHTML = `
            <div class="alert alert-info text-center">
                <i class="fas fa-info-circle"></i> Directory selected. Click "Scan Directory" to browse files.
            </div>
        `;
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Scan directory functionality
    function scanDirectory() {
        if (!selectedDirectory) {
            alert('Please select a directory first.');
            return;
        }

        scanDirectoryBtn.disabled = true;
        scanDirectoryBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';

        fileListContainer.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Scanning...</span>
                </div>
                <p class="mt-2">Scanning directory for files...</p>
            </div>
        `;

        fetch('/scan-directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `directory=${encodeURIComponent(selectedDirectory)}`
        })
        .then(response => response.json())
        .then(data => {
            scanDirectoryBtn.disabled = false;
            scanDirectoryBtn.innerHTML = '<i class="fas fa-search"></i> Scan Directory';

            if (data.status === 'success') {
                scannedFiles = data.files || [];
                displayFileList(scannedFiles);
            } else {
                fileListContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error scanning directory:', error);
            scanDirectoryBtn.disabled = false;
            scanDirectoryBtn.innerHTML = '<i class="fas fa-search"></i> Scan Directory';
            fileListContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error scanning directory.
                </div>
            `;
        });
    }

    function displayFileList(files) {
        if (!files || files.length === 0) {
            fileListContainer.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle"></i> No supported files found in the selected directory.
                </div>
            `;
            return;
        }

        // Apply document type filters
        const filteredFiles = applyDocumentTypeFilters(files);

        let html = `
            <div class="mb-3">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-0"><strong>Files found:</strong> ${filteredFiles.length} of ${files.length}</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">Select files to search through</small>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" class="form-check-input" id="select-all-checkbox">
                            </th>
                            <th>File Name</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        filteredFiles.forEach((file, index) => {
            const fileIcon = getFileIcon(file.extension);
            html += `
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input file-checkbox" data-file-index="${index}" data-file-path="${file.path}">
                    </td>
                    <td>
                        <i class="${fileIcon}"></i> ${file.name}
                    </td>
                    <td>
                        <span class="badge bg-secondary">${file.extension.toUpperCase()}</span>
                    </td>
                    <td>${formatFileSize(file.size)}</td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="viewFile('${file.path}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="downloadFile('${file.path}')">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        fileListContainer.innerHTML = html;

        // Enable selection buttons
        selectAllFilesBtn.disabled = false;
        clearSelectionBtn.disabled = false;

        // Add event listeners
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const fileCheckboxes = document.querySelectorAll('.file-checkbox');

        selectAllCheckbox.addEventListener('change', function() {
            fileCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedFiles();
        });

        fileCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedFiles);
        });

        updateSelectedFiles();
    }

    function getFileIcon(extension) {
        const iconMap = {
            pdf: 'fas fa-file-pdf text-danger',
            doc: 'fas fa-file-word text-primary',
            docx: 'fas fa-file-word text-primary',
            xls: 'fas fa-file-excel text-success',
            xlsx: 'fas fa-file-excel text-success',
            csv: 'fas fa-file-excel text-success',
            ppt: 'fas fa-file-powerpoint text-warning',
            pptx: 'fas fa-file-powerpoint text-warning',
            jpg: 'fas fa-file-image text-info',
            jpeg: 'fas fa-file-image text-info',
            png: 'fas fa-file-image text-info',
            gif: 'fas fa-file-image text-info',
            bmp: 'fas fa-file-image text-info',
            tiff: 'fas fa-file-image text-info',
            tif: 'fas fa-file-image text-info',
            txt: 'fas fa-file-alt text-secondary',
            md: 'fas fa-file-alt text-secondary',
            log: 'fas fa-file-alt text-secondary'
        };

        return iconMap[extension.toLowerCase()] || 'fas fa-file text-secondary';
    }

    function applyDocumentTypeFilters(files = scannedFiles) {
        const enabledTypes = [];

        Object.entries(docTypeFilters).forEach(([type, checkbox]) => {
            if (checkbox.checked) {
                enabledTypes.push(...fileTypeMap[type]);
            }
        });

        const filtered = files.filter(file =>
            enabledTypes.includes(file.extension.toLowerCase())
        );

        if (files === scannedFiles) {
            displayFileList(filtered);
            return filtered;
        }

        return filtered;
    }

    function updateSelectedFiles() {
        const checkboxes = document.querySelectorAll('.file-checkbox:checked');
        selectedFiles = Array.from(checkboxes).map(cb => cb.dataset.filePath);

        // Update search button state
        searchBtn.disabled = selectedFiles.length === 0 || !searchQuery.value.trim();

        // Update select all checkbox state
        const allCheckboxes = document.querySelectorAll('.file-checkbox');
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.indeterminate = checkboxes.length > 0 && checkboxes.length < allCheckboxes.length;
            selectAllCheckbox.checked = checkboxes.length === allCheckboxes.length && allCheckboxes.length > 0;
        }
    }

    function selectAllFiles() {
        const checkboxes = document.querySelectorAll('.file-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = true);
        updateSelectedFiles();
    }

    function clearSelection() {
        const checkboxes = document.querySelectorAll('.file-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = false);
        updateSelectedFiles();
    }

    // Search functionality
    searchQuery.addEventListener('input', function() {
        searchBtn.disabled = selectedFiles.length === 0 || !this.value.trim();
    });

    function performSearch(e) {
        e.preventDefault();

        if (selectedFiles.length === 0) {
            alert('Please select at least one file to search.');
            return;
        }

        if (!searchQuery.value.trim()) {
            alert('Please enter a search query.');
            return;
        }

        if (searchInProgress) {
            return;
        }

        searchInProgress = true;
        showSearchProgress();

        // Prepare search data
        const searchData = {
            query: searchQuery.value.trim(),
            files: selectedFiles,
            advanced_options: {
                case_sensitive: document.getElementById('case-sensitive').checked,
                whole_word: document.getElementById('whole-word').checked,
                regex: document.getElementById('use-regex').checked,
                search_mode: document.getElementById('search-mode').value
            },
            ocr_options: {
                enabled: document.getElementById('enable-ocr').checked,
                handwriting: document.getElementById('handwriting-recognition').checked,
                language: document.getElementById('ocr-language').value,
                dpi: parseInt(document.getElementById('ocr-dpi').value),
                preprocessing: {
                    bilateral_filter: document.getElementById('bilateral-filter').checked,
                    gaussian_blur: document.getElementById('gaussian-blur').checked,
                    contrast_enhancement: document.getElementById('contrast-enhancement').checked
                }
            }
        };

        // Start search
        fetch('/search-files', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(searchData)
        })
        .then(response => response.json())
        .then(data => {
            searchInProgress = false;
            hideSearchProgress();

            if (data.status === 'success') {
                currentSearchResults = data.results || [];
                displaySearchResults(currentSearchResults);
            } else {
                alert(`Search failed: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            searchInProgress = false;
            hideSearchProgress();
            alert('An error occurred during search.');
        });
    }

    function showSearchProgress() {
        searchProgress.style.display = 'block';
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';

        // Simulate progress updates
        let progress = 0;
        const progressBar = document.getElementById('search-progress-bar');
        const currentFileSpan = document.getElementById('current-file');
        const fileProgressSpan = document.getElementById('file-progress');
        const matchesFoundSpan = document.getElementById('matches-found');
        const searchStatusSpan = document.getElementById('search-status');

        const progressInterval = setInterval(() => {
            if (!searchInProgress) {
                clearInterval(progressInterval);
                return;
            }

            progress += Math.random() * 10;
            if (progress > 95) progress = 95;

            progressBar.style.width = progress + '%';
            progressBar.setAttribute('aria-valuenow', progress);

            // Update status text
            const fileIndex = Math.floor((progress / 100) * selectedFiles.length);
            if (fileIndex < selectedFiles.length) {
                const fileName = selectedFiles[fileIndex].split('/').pop();
                currentFileSpan.textContent = fileName;
                fileProgressSpan.textContent = `${fileIndex + 1} / ${selectedFiles.length}`;
            }

            searchStatusSpan.textContent = 'Searching files...';
        }, 500);
    }

    function hideSearchProgress() {
        searchProgress.style.display = 'none';
        searchBtn.disabled = selectedFiles.length === 0 || !searchQuery.value.trim();
        searchBtn.innerHTML = '<i class="fas fa-search"></i> Search Files';

        // Complete progress bar
        const progressBar = document.getElementById('search-progress-bar');
        progressBar.style.width = '100%';
        document.getElementById('search-status').textContent = 'Search completed';
    }

    function displaySearchResults(results) {
        const resultsContainer = document.getElementById('results-container');
        const resultsCount = document.getElementById('results-count');

        resultsCount.textContent = results.length;
        searchResults.style.display = 'block';

        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-search"></i> No matches found for "${searchQuery.value}".
                </div>
            `;
            return;
        }

        let html = '';
        results.forEach((result, index) => {
            const fileName = result.name || result.path.split('/').pop();
            const fileIcon = getFileIcon(result.doc_type || 'txt');

            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h6 class="mb-0">
                                    <i class="${fileIcon}"></i> ${fileName}
                                    <span class="badge bg-primary ms-2">${result.match_count} matches</span>
                                </h6>
                                <small class="text-muted">${result.path}</small>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="viewFile('${result.path}')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="downloadFile('${result.path}')">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#matches-${index}">
                                        <i class="fas fa-chevron-down"></i> Matches
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="collapse" id="matches-${index}">
                        <div class="card-body">
                            ${result.matches.map(match => `
                                <div class="mb-2 p-2 bg-light rounded">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <small class="text-muted">Line ${match.line}:</small>
                                            <div class="mt-1">${highlightSearchTerms(match.context, searchQuery.value)}</div>
                                        </div>
                                        ${match.page_link ? `
                                            <a href="${match.page_link}" class="btn btn-sm btn-outline-primary ms-2" target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        });

        resultsContainer.innerHTML = html;

        // Show PDF creation section if there are results
        document.getElementById('pdf-creation').style.display = 'block';
    }

    function highlightSearchTerms(text, searchTerm) {
        if (!text || !searchTerm) return text;

        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    // Expand/Collapse all results
    document.getElementById('expand-all-results').addEventListener('click', function() {
        document.querySelectorAll('#results-container .collapse').forEach(collapse => {
            new bootstrap.Collapse(collapse, { show: true });
        });
    });

    document.getElementById('collapse-all-results').addEventListener('click', function() {
        document.querySelectorAll('#results-container .collapse.show').forEach(collapse => {
            new bootstrap.Collapse(collapse, { hide: true });
        });
    });

    // File operations
    window.viewFile = function(filePath) {
        window.open(`/open-document?path=${encodeURIComponent(filePath)}`, '_blank');
    };

    window.downloadFile = function(filePath) {
        window.open(`/download-document?path=${encodeURIComponent(filePath)}`, '_blank');
    };

    // PDF creation functionality
    document.getElementById('create-single-pdf').addEventListener('click', function() {
        if (currentSearchResults.length === 0) {
            alert('No search results available for PDF creation.');
            return;
        }

        // Get the first selected file from results
        const selectedResult = currentSearchResults[0];
        if (!selectedResult) {
            alert('Please select a file from the search results.');
            return;
        }

        const createPdfData = {
            file_path: selectedResult.path,
            ocr_options: {
                language: document.getElementById('ocr-language').value,
                dpi: parseInt(document.getElementById('ocr-dpi').value),
                preprocessing: {
                    bilateral_filter: document.getElementById('bilateral-filter').checked,
                    gaussian_blur: document.getElementById('gaussian-blur').checked,
                    contrast_enhancement: document.getElementById('contrast-enhancement').checked
                }
            }
        };

        fetch('/create-searchable-pdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(createPdfData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('Searchable PDF created successfully!');
                if (data.download_url) {
                    window.open(data.download_url, '_blank');
                }
            } else {
                alert(`PDF creation failed: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('PDF creation error:', error);
            alert('An error occurred during PDF creation.');
        });
    });

    document.getElementById('create-batch-pdf').addEventListener('click', function() {
        if (currentSearchResults.length === 0) {
            alert('No search results available for batch PDF creation.');
            return;
        }

        const filePaths = currentSearchResults.map(result => result.path);

        const batchPdfData = {
            file_paths: filePaths,
            ocr_options: {
                language: document.getElementById('ocr-language').value,
                dpi: parseInt(document.getElementById('ocr-dpi').value),
                preprocessing: {
                    bilateral_filter: document.getElementById('bilateral-filter').checked,
                    gaussian_blur: document.getElementById('gaussian-blur').checked,
                    contrast_enhancement: document.getElementById('contrast-enhancement').checked
                }
            }
        };

        fetch('/create-batch-searchable-pdfs', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(batchPdfData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert(`Batch PDF creation completed! ${data.created_count} PDFs created.`);
                if (data.download_urls && data.download_urls.length > 0) {
                    data.download_urls.forEach(url => {
                        window.open(url, '_blank');
                    });
                }
            } else {
                alert(`Batch PDF creation failed: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Batch PDF creation error:', error);
            alert('An error occurred during batch PDF creation.');
        });
    });
});
</script>
{% endblock %}
