<script>
// Direct Search v3.0 - Search Results Display Functions

Object.assign(DirectSearchApp.prototype, {
    
    displaySearchResults(results) {
        const resultsSection = document.getElementById('search-results');
        const resultsContainer = document.getElementById('results-container');
        const resultsCount = document.getElementById('results-count');
        
        resultsCount.textContent = results.length;
        resultsSection.style.display = 'block';
        resultsSection.classList.add('fade-in');
        
        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="alert alert-warning" style="text-align: center;">
                    <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h4>No matches found</h4>
                    <p>No matches found for "${document.getElementById('search-query').value}" in the selected files.</p>
                    <p style="font-size: 0.875rem; color: var(--secondary-color);">Try adjusting your search terms or selecting different files.</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        results.forEach((result, index) => {
            const fileName = result.name || result.path.split('/').pop();
            const fileIcon = this.getFileIcon(result.doc_type || result.extension || 'txt');
            const matchCount = result.match_count || result.matches?.length || 0;
            
            html += `
                <div class="result-card slide-up" style="animation-delay: ${index * 0.1}s;">
                    <div class="result-header" onclick="this.parentElement.querySelector('.result-body').classList.toggle('expanded'); this.parentElement.querySelector('.result-body').style.display = this.parentElement.querySelector('.result-body').style.display === 'block' ? 'none' : 'block';">
                        <div class="result-title">
                            <i class="${fileIcon}" style="color: ${this.getFileIconColor(result.doc_type || result.extension)};"></i>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 0.25rem;">${fileName}</div>
                                <div style="font-size: 0.875rem; color: var(--secondary-color);">${result.path}</div>
                            </div>
                            <span class="result-badge">${matchCount} matches</span>
                        </div>
                        <div class="result-actions" onclick="event.stopPropagation();">
                            <button class="btn btn-outline btn-sm" onclick="window.directSearchApp.viewFile('${result.path}')" title="View File">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="window.directSearchApp.downloadFile('${result.path}')" title="Download File">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="window.directSearchApp.toggleResultExpansion(this)" title="Toggle Matches">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                    <div class="result-body">
                        ${this.renderMatches(result.matches || [], document.getElementById('search-query').value)}
                    </div>
                </div>
            `;
        });
        
        resultsContainer.innerHTML = html;
        
        // Scroll to results
        setTimeout(() => {
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 300);
    },
    
    renderMatches(matches, searchTerm) {
        if (!matches || matches.length === 0) {
            return `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No specific match details available.
                </div>
            `;
        }
        
        let html = '';
        matches.forEach((match, index) => {
            const highlightedContext = this.highlightSearchTerms(match.context || match.text || '', searchTerm);
            
            html += `
                <div class="match-item" style="animation: slideUp 0.3s ease-out ${index * 0.05}s both;">
                    <div class="match-line">
                        <i class="fas fa-map-marker-alt" style="color: var(--primary-color);"></i>
                        Line ${match.line || match.line_number || 'Unknown'}
                        ${match.page ? `| Page ${match.page}` : ''}
                        ${match.confidence ? `| Confidence: ${Math.round(match.confidence * 100)}%` : ''}
                    </div>
                    <div class="match-context">${highlightedContext}</div>
                    ${match.page_link ? `
                        <div style="margin-top: 0.75rem;">
                            <a href="${match.page_link}" class="btn btn-outline btn-sm" target="_blank">
                                <i class="fas fa-external-link-alt"></i> View in Document
                            </a>
                        </div>
                    ` : ''}
                </div>
            `;
        });
        
        return html;
    },
    
    highlightSearchTerms(text, searchTerm) {
        if (!text || !searchTerm) return text;
        
        // Escape special regex characters in search term
        const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        
        // Create regex with global and case-insensitive flags
        const regex = new RegExp(`(${escapedTerm})`, 'gi');
        
        // Replace matches with highlighted spans
        return text.replace(regex, '<span class="match-highlight">$1</span>');
    },
    
    getFileIconColor(extension) {
        const colorMap = {
            pdf: '#dc2626',
            doc: '#2563eb',
            docx: '#2563eb',
            xls: '#059669',
            xlsx: '#059669',
            csv: '#059669',
            ppt: '#d97706',
            pptx: '#d97706',
            jpg: '#0891b2',
            jpeg: '#0891b2',
            png: '#0891b2',
            gif: '#0891b2',
            bmp: '#0891b2',
            tiff: '#0891b2',
            tif: '#0891b2',
            txt: '#64748b',
            md: '#64748b',
            log: '#64748b'
        };
        
        return colorMap[extension?.toLowerCase()] || '#64748b';
    },
    
    toggleResultExpansion(button) {
        const resultCard = button.closest('.result-card');
        const resultBody = resultCard.querySelector('.result-body');
        const chevron = button.querySelector('i');
        
        if (resultBody.style.display === 'block') {
            resultBody.style.display = 'none';
            resultBody.classList.remove('expanded');
            chevron.style.transform = 'rotate(0deg)';
        } else {
            resultBody.style.display = 'block';
            resultBody.classList.add('expanded');
            chevron.style.transform = 'rotate(180deg)';
        }
    },
    
    // Enhanced file operations with better error handling
    async viewFile(filePath) {
        try {
            const response = await fetch(`/open-document?path=${encodeURIComponent(filePath)}`);
            if (response.ok) {
                window.open(`/open-document?path=${encodeURIComponent(filePath)}`, '_blank');
            } else {
                this.showNotification('Unable to open file. File may not exist or be accessible.', 'warning');
            }
        } catch (error) {
            console.error('Error opening file:', error);
            this.showNotification('Error opening file.', 'danger');
        }
    },
    
    async downloadFile(filePath) {
        try {
            // Create a temporary link to trigger download
            const link = document.createElement('a');
            link.href = `/download-document?path=${encodeURIComponent(filePath)}`;
            link.download = filePath.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.showNotification('Download started...', 'success');
        } catch (error) {
            console.error('Error downloading file:', error);
            this.showNotification('Error downloading file.', 'danger');
        }
    },
    
    // Enhanced search functionality with better error handling
    async performAdvancedSearch() {
        // This method can be extended for more advanced search features
        // such as fuzzy matching, proximity search, etc.
        
        const searchOptions = {
            fuzzy: document.getElementById('fuzzy-search')?.checked || false,
            proximity: document.getElementById('proximity-search')?.value || 0,
            stemming: document.getElementById('stemming')?.checked || false
        };
        
        // Implementation for advanced search features
        console.log('Advanced search options:', searchOptions);
    },
    
    // Export search results functionality
    exportResults(format = 'json') {
        if (this.currentSearchResults.length === 0) {
            this.showNotification('No search results to export.', 'warning');
            return;
        }
        
        let content = '';
        let filename = '';
        let mimeType = '';
        
        switch (format) {
            case 'json':
                content = JSON.stringify(this.currentSearchResults, null, 2);
                filename = `search_results_${new Date().toISOString().split('T')[0]}.json`;
                mimeType = 'application/json';
                break;
            case 'csv':
                content = this.convertResultsToCSV(this.currentSearchResults);
                filename = `search_results_${new Date().toISOString().split('T')[0]}.csv`;
                mimeType = 'text/csv';
                break;
            case 'txt':
                content = this.convertResultsToText(this.currentSearchResults);
                filename = `search_results_${new Date().toISOString().split('T')[0]}.txt`;
                mimeType = 'text/plain';
                break;
        }
        
        // Create and trigger download
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
        
        this.showNotification(`Results exported as ${format.toUpperCase()}`, 'success');
    },
    
    convertResultsToCSV(results) {
        const headers = ['File Name', 'File Path', 'Match Count', 'Matches'];
        const rows = results.map(result => [
            result.name || result.path.split('/').pop(),
            result.path,
            result.match_count || result.matches?.length || 0,
            (result.matches || []).map(match => `Line ${match.line}: ${match.context}`).join('; ')
        ]);
        
        return [headers, ...rows].map(row => 
            row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    },
    
    convertResultsToText(results) {
        return results.map(result => {
            const fileName = result.name || result.path.split('/').pop();
            const matches = (result.matches || []).map(match => 
                `  Line ${match.line}: ${match.context}`
            ).join('\n');
            
            return `File: ${fileName}\nPath: ${result.path}\nMatches: ${result.match_count || result.matches?.length || 0}\n${matches}\n${'='.repeat(50)}`;
        }).join('\n\n');
    }
});

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (window.directSearchApp) {
        // Ctrl+F or Cmd+F to focus search input
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            document.getElementById('search-query').focus();
        }
        
        // Escape to close modal
        if (e.key === 'Escape') {
            const modal = document.getElementById('directory-browser-modal');
            if (modal.style.display === 'block') {
                window.directSearchApp.closeModal();
            }
        }
        
        // Ctrl+A or Cmd+A to select all files (when file list is focused)
        if ((e.ctrlKey || e.metaKey) && e.key === 'a' && document.activeElement.closest('#file-list-container')) {
            e.preventDefault();
            window.directSearchApp.selectAllFiles();
        }
    }
});
</script>
