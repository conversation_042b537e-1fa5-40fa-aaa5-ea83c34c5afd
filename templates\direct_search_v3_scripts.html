<script>
// Direct Search v3.0 - Modern JavaScript Implementation
class DirectSearchApp {
    constructor() {
        this.selectedDirectory = '';
        this.scannedFiles = [];
        this.selectedFiles = [];
        this.searchInProgress = false;
        this.currentSearchResults = [];
        
        // File type mappings
        this.fileTypeMap = {
            pdf: ['pdf'],
            word: ['doc', 'docx'],
            excel: ['xls', 'xlsx', 'csv'],
            powerpoint: ['ppt', 'pptx'],
            images: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif'],
            text: ['txt', 'md', 'log', 'json', 'xml', 'html', 'css', 'js', 'py', 'java', 'cpp', 'c', 'h']
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupDocumentTypeFilters();
        console.log('DirectSearch v3.0 initialized successfully!');
    }
    
    bindEvents() {
        // Directory selection
        document.getElementById('browse-directory-btn').addEventListener('click', () => this.openDirectoryBrowser());
        document.getElementById('scan-directory-btn').addEventListener('click', () => this.scanDirectory());
        
        // File selection
        document.getElementById('select-all-files').addEventListener('click', () => this.selectAllFiles());
        document.getElementById('clear-selection').addEventListener('click', () => this.clearSelection());
        
        // Search functionality
        document.getElementById('search-form').addEventListener('submit', (e) => this.performSearch(e));
        document.getElementById('search-query').addEventListener('input', (e) => this.updateSearchButton());
        
        // Advanced options toggle
        document.getElementById('toggle-advanced').addEventListener('click', () => this.toggleAdvancedOptions());
        
        // Modal events
        document.getElementById('close-modal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancel-modal').addEventListener('click', () => this.closeModal());
        document.getElementById('select-directory-btn').addEventListener('click', () => this.selectCurrentDirectory());
        
        // Results actions
        document.getElementById('expand-all-results').addEventListener('click', () => this.expandAllResults());
        document.getElementById('collapse-all-results').addEventListener('click', () => this.collapseAllResults());
        
        // Close modal on backdrop click
        document.querySelector('.modal-backdrop').addEventListener('click', () => this.closeModal());
    }
    
    setupDocumentTypeFilters() {
        const filters = document.querySelectorAll('.filter-toggle');
        filters.forEach(filter => {
            filter.addEventListener('click', () => {
                const checkbox = filter.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
                this.applyDocumentTypeFilters();
            });
        });
    }
    
    async openDirectoryBrowser() {
        document.getElementById('directory-browser-modal').style.display = 'block';
        document.body.style.overflow = 'hidden';
        await this.loadDirectoryContents('/');
    }
    
    closeModal() {
        document.getElementById('directory-browser-modal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    
    async loadDirectoryContents(path) {
        const breadcrumb = document.getElementById('directory-breadcrumb');
        const contents = document.getElementById('directory-contents');
        
        // Update breadcrumb
        this.updateBreadcrumb(path);
        
        // Show loading
        contents.innerHTML = `
            <div style="text-align: center; padding: 2rem;">
                <div class="loading-spinner"></div>
                <p style="margin-top: 1rem; color: var(--secondary-color);">Loading directory contents...</p>
            </div>
        `;
        
        try {
            const response = await fetch('/browse-directory', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ path: path })
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displayDirectoryContents(data.contents || data.directories || [], path);
            } else {
                contents.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message || 'Failed to load directory'}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading directory:', error);
            contents.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error loading directory contents.
                </div>
            `;
        }
    }
    
    updateBreadcrumb(path) {
        const breadcrumb = document.getElementById('directory-breadcrumb');
        const parts = path.split('/').filter(part => part);
        
        let html = `
            <div class="breadcrumb">
                <div class="breadcrumb-item">
                    <a href="#" class="breadcrumb-link" data-path="/">
                        <i class="fas fa-home"></i> Root
                    </a>
                </div>
        `;
        
        let currentPath = '';
        parts.forEach((part, index) => {
            currentPath += '/' + part;
            html += '<span class="breadcrumb-separator">/</span>';
            
            if (index === parts.length - 1) {
                html += `
                    <div class="breadcrumb-item">
                        <span class="breadcrumb-current">${part}</span>
                    </div>
                `;
            } else {
                html += `
                    <div class="breadcrumb-item">
                        <a href="#" class="breadcrumb-link" data-path="${currentPath}">${part}</a>
                    </div>
                `;
            }
        });
        
        html += '</div>';
        breadcrumb.innerHTML = html;
        
        // Add click listeners
        breadcrumb.querySelectorAll('.breadcrumb-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.loadDirectoryContents(link.dataset.path);
            });
        });
    }
    
    displayDirectoryContents(contents, currentPath) {
        const contentsDiv = document.getElementById('directory-contents');
        const selectBtn = document.getElementById('select-directory-btn');
        
        if (!contents || contents.length === 0) {
            contentsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> This directory is empty.
                </div>
            `;
            selectBtn.disabled = false;
            selectBtn.onclick = () => this.selectDirectory(currentPath);
            return;
        }
        
        let html = '';
        
        // Add parent directory link if not at root
        if (currentPath !== '/') {
            const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
            html += `
                <a href="#" class="directory-item" data-path="${parentPath}">
                    <i class="fas fa-level-up-alt directory-icon" style="color: var(--secondary-color);"></i>
                    <span class="directory-name">.. (Parent Directory)</span>
                </a>
            `;
        }
        
        // Handle different response formats
        let directories = [];
        if (Array.isArray(contents)) {
            // If contents is an array of strings (directory names)
            directories = contents.map(name => ({ name, type: 'directory' }));
        } else if (contents.directories) {
            // If contents has a directories property
            directories = contents.directories.map(name => ({ name, type: 'directory' }));
        }
        
        // Sort directories alphabetically
        directories.sort((a, b) => a.name.localeCompare(b.name));
        
        directories.forEach(item => {
            const itemPath = currentPath === '/' ? `/${item.name}` : `${currentPath}/${item.name}`;
            html += `
                <a href="#" class="directory-item" data-path="${itemPath}" data-type="directory">
                    <i class="fas fa-folder directory-icon" style="color: #f59e0b;"></i>
                    <span class="directory-name">${item.name}</span>
                </a>
            `;
        });
        
        if (directories.length === 0 && currentPath !== '/') {
            html += `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> No subdirectories found.
                </div>
            `;
        }
        
        contentsDiv.innerHTML = html;
        
        // Add click listeners
        contentsDiv.querySelectorAll('.directory-item').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                if (link.dataset.type === 'directory' || link.dataset.path !== currentPath) {
                    this.loadDirectoryContents(link.dataset.path);
                }
            });
        });
        
        // Enable select button
        selectBtn.disabled = false;
        selectBtn.onclick = () => this.selectDirectory(currentPath);
    }
    
    selectDirectory(path) {
        this.selectedDirectory = path;
        document.getElementById('directory-input').value = path;
        document.getElementById('scan-directory-btn').disabled = false;
        this.closeModal();
        this.showNotification('Directory selected successfully!', 'success');
    }
    
    selectCurrentDirectory() {
        // Get the current path from breadcrumb
        const breadcrumbItems = document.querySelectorAll('.breadcrumb-current');
        if (breadcrumbItems.length > 0) {
            const currentDir = breadcrumbItems[0].textContent;
            const fullPath = this.getCurrentPathFromBreadcrumb();
            this.selectDirectory(fullPath);
        } else {
            this.selectDirectory('/');
        }
    }
    
    getCurrentPathFromBreadcrumb() {
        const breadcrumbLinks = document.querySelectorAll('.breadcrumb-link');
        const currentSpan = document.querySelector('.breadcrumb-current');
        
        let path = '';
        breadcrumbLinks.forEach(link => {
            if (link.dataset.path !== '/') {
                path = link.dataset.path;
            }
        });
        
        if (currentSpan) {
            path += (path === '' ? '' : '/') + currentSpan.textContent;
        }
        
        return path || '/';
    }
    
    async scanDirectory() {
        if (!this.selectedDirectory) {
            this.showNotification('Please select a directory first.', 'warning');
            return;
        }
        
        const scanBtn = document.getElementById('scan-directory-btn');
        const originalText = scanBtn.innerHTML;
        
        scanBtn.disabled = true;
        scanBtn.innerHTML = '<div class="loading-spinner"></div> Scanning...';
        
        const fileListContainer = document.getElementById('file-list-container');
        fileListContainer.innerHTML = `
            <div style="text-align: center; padding: 2rem;">
                <div class="loading-spinner"></div>
                <p style="margin-top: 1rem; color: var(--secondary-color);">Scanning directory for files...</p>
            </div>
        `;
        
        try {
            const response = await fetch('/scan-directory', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `directory=${encodeURIComponent(this.selectedDirectory)}`
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.scannedFiles = data.files || [];
                this.displayFileList(this.scannedFiles);
                this.showNotification(`Found ${this.scannedFiles.length} files`, 'success');
            } else {
                fileListContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error scanning directory:', error);
            fileListContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error scanning directory.
                </div>
            `;
        } finally {
            scanBtn.disabled = false;
            scanBtn.innerHTML = originalText;
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} fade-in`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            box-shadow: var(--shadow-lg);
        `;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 1.2rem; cursor: pointer;">×</button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    toggleAdvancedOptions() {
        const panel = document.getElementById('advanced-options');
        const chevron = document.getElementById('advanced-chevron');
        
        if (panel.style.display === 'none') {
            panel.style.display = 'block';
            panel.classList.add('slide-up');
            chevron.style.transform = 'rotate(180deg)';
        } else {
            panel.style.display = 'none';
            chevron.style.transform = 'rotate(0deg)';
        }
    }
    
    updateSearchButton() {
        const query = document.getElementById('search-query').value.trim();
        const searchBtn = document.getElementById('search-btn');
        searchBtn.disabled = this.selectedFiles.length === 0 || !query;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.directSearchApp = new DirectSearchApp();
});
</script>
