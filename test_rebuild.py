#!/usr/bin/env python3
"""
Test script to verify the rebuilt Direct Search and Indexing functionality.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_homepage():
    """Test the homepage loads correctly."""
    print("Testing homepage...")
    response = requests.get(f"{BASE_URL}/")
    assert response.status_code == 200
    assert "TextFinder" in response.text
    print("✓ Homepage loads successfully")

def test_direct_search_page():
    """Test the direct search page loads correctly."""
    print("Testing direct search page...")
    response = requests.get(f"{BASE_URL}/direct-search")
    assert response.status_code == 200
    assert "Direct File Search" in response.text
    assert "File Selection" in response.text
    assert "Search Options" in response.text
    print("✓ Direct search page loads successfully")

def test_indexing_page():
    """Test the indexing page loads correctly."""
    print("Testing indexing page...")
    response = requests.get(f"{BASE_URL}/indexing")
    assert response.status_code == 200
    assert "File Indexing" in response.text
    assert "Folder Management" in response.text
    assert "Search Indexed Folders" in response.text
    print("✓ Indexing page loads successfully")

def test_browse_directory_api():
    """Test the browse directory API."""
    print("Testing browse directory API...")
    response = requests.post(
        f"{BASE_URL}/browse-directory",
        headers={"Content-Type": "application/json"},
        data=json.dumps({"path": "/"})
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    print("✓ Browse directory API works correctly")

def test_indexed_folders_api():
    """Test the indexed folders API."""
    print("Testing indexed folders API...")
    response = requests.get(f"{BASE_URL}/api/indexed-folders")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "folders" in data
    print(f"✓ Indexed folders API works correctly ({len(data['folders'])} folders found)")

def test_scan_directory_api():
    """Test the scan directory API with the current directory."""
    print("Testing scan directory API...")
    response = requests.post(
        f"{BASE_URL}/scan-directory",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        data={"directory": "."}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "files" in data
    print(f"✓ Scan directory API works correctly ({len(data['files'])} files found)")

def test_search_files_api():
    """Test the search files API with a simple search."""
    print("Testing search files API...")
    
    # First scan the current directory to get some files
    scan_response = requests.post(
        f"{BASE_URL}/scan-directory",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        data={"directory": "."}
    )
    scan_data = scan_response.json()
    
    if scan_data["status"] == "success" and scan_data["files"]:
        # Use the first few files for testing
        test_files = [f["path"] for f in scan_data["files"][:3]]
        
        search_data = {
            "query": "import",
            "files": test_files,
            "advanced_options": {
                "case_sensitive": False,
                "whole_word": False,
                "regex": False,
                "search_mode": "OR"
            },
            "ocr_options": {
                "enabled": False
            }
        }
        
        response = requests.post(
            f"{BASE_URL}/search-files",
            headers={"Content-Type": "application/json"},
            data=json.dumps(search_data)
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        print(f"✓ Search files API works correctly ({len(data.get('results', []))} results found)")
    else:
        print("⚠ Skipping search files test - no files found to search")

def test_responsive_design():
    """Test that pages include responsive design elements."""
    print("Testing responsive design...")
    
    # Check direct search page for Bootstrap classes
    response = requests.get(f"{BASE_URL}/direct-search")
    content = response.text
    assert "container-fluid" in content
    assert "row" in content
    assert "col-" in content
    assert "btn" in content
    assert "card" in content
    print("✓ Direct search page includes responsive design elements")
    
    # Check indexing page for Bootstrap classes
    response = requests.get(f"{BASE_URL}/indexing")
    content = response.text
    assert "container-fluid" in content
    assert "row" in content
    assert "col-" in content
    assert "btn" in content
    assert "card" in content
    print("✓ Indexing page includes responsive design elements")

def test_javascript_functionality():
    """Test that JavaScript files are included and functional."""
    print("Testing JavaScript functionality...")
    
    # Check direct search page for JavaScript
    response = requests.get(f"{BASE_URL}/direct-search")
    content = response.text
    assert "<script>" in content
    assert "addEventListener" in content
    assert "fetch" in content
    print("✓ Direct search page includes JavaScript functionality")
    
    # Check indexing page for JavaScript
    response = requests.get(f"{BASE_URL}/indexing")
    content = response.text
    assert "<script>" in content
    assert "addEventListener" in content
    assert "fetch" in content
    print("✓ Indexing page includes JavaScript functionality")

def run_all_tests():
    """Run all tests."""
    print("=" * 60)
    print("TEXTFINDER REBUILD VALIDATION TESTS")
    print("=" * 60)
    
    tests = [
        test_homepage,
        test_direct_search_page,
        test_indexing_page,
        test_browse_directory_api,
        test_indexed_folders_api,
        test_scan_directory_api,
        test_search_files_api,
        test_responsive_design,
        test_javascript_functionality
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"✗ {test.__name__} failed: {str(e)}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"TEST RESULTS: {passed} passed, {failed} failed")
    print("=" * 60)
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! The TextFinder rebuild is successful.")
    else:
        print("⚠ Some tests failed. Please review the issues above.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
