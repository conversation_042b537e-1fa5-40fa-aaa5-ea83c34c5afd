<script>
// Indexing v3.0 - Additional JavaScript Functions (Part 2)

// Extend the IndexingApp class with additional methods
Object.assign(IndexingApp.prototype, {
    
    async loadIndexedFolders() {
        try {
            const response = await fetch('/api/indexed-folders');
            const data = await response.json();
            
            if (data.status === 'success') {
                this.indexedFolders = data.folders || [];
                this.displayIndexedFolders(this.indexedFolders);
                this.updateFolderFilters(this.indexedFolders);
                this.updateIndexedSearchButton();
            } else {
                console.error('Failed to load indexed folders:', data.message);
            }
        } catch (error) {
            console.error('Error loading indexed folders:', error);
        }
    },
    
    displayIndexedFolders(folders) {
        const container = document.getElementById('indexed-folders-container');
        const noFoldersMessage = document.getElementById('no-folders-message');
        
        if (!folders || folders.length === 0) {
            noFoldersMessage.style.display = 'block';
            return;
        }
        
        noFoldersMessage.style.display = 'none';
        
        let html = '<div style="display: flex; flex-direction: column; gap: 1rem;">';
        
        folders.forEach((folder, index) => {
            const folderName = folder.path || folder.name || folder;
            const fileCount = folder.file_count || folder.files || 0;
            const lastIndexed = folder.last_indexed || folder.updated || 'Never';
            const status = folder.status || 'indexed';
            
            const statusColor = status === 'indexed' ? 'var(--success-color)' : 
                               status === 'indexing' ? 'var(--warning-color)' : 
                               'var(--danger-color)';
            
            html += `
                <div class="card slide-up" style="animation-delay: ${index * 0.1}s;">
                    <div class="card-body" style="padding: 1rem;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1;">
                                <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 0.5rem;">
                                    <i class="fas fa-folder" style="color: #f59e0b; font-size: 1.25rem;"></i>
                                    <div>
                                        <h5 style="margin: 0; font-weight: 600;">${folderName}</h5>
                                        <div style="font-size: 0.875rem; color: var(--secondary-color); margin-top: 0.25rem;">
                                            <span><strong>Files:</strong> ${fileCount}</span>
                                            <span style="margin-left: 1rem;"><strong>Last Indexed:</strong> ${lastIndexed}</span>
                                            <span style="margin-left: 1rem;">
                                                <span style="color: ${statusColor}; font-weight: 600;">
                                                    <i class="fas fa-circle" style="font-size: 0.5rem;"></i> ${status.toUpperCase()}
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="display: flex; gap: 0.5rem;">
                                <button class="btn btn-outline btn-sm" onclick="window.indexingApp.reindexFolder('${folderName}')" title="Re-index Folder">
                                    <i class="fas fa-sync"></i>
                                </button>
                                <button class="btn btn-outline btn-sm" onclick="window.indexingApp.viewFolderDetails('${folderName}')" title="View Details">
                                    <i class="fas fa-info-circle"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="window.indexingApp.removeFolderFromIndex('${folderName}')" title="Remove from Index">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
    },
    
    updateFolderFilters(folders) {
        const filtersContainer = document.getElementById('folder-filters');
        
        if (!folders || folders.length === 0) {
            filtersContainer.innerHTML = '<span style="color: var(--secondary-color); font-style: italic;">No indexed folders available</span>';
            return;
        }
        
        let html = '';
        folders.forEach(folder => {
            const folderName = folder.path || folder.name || folder;
            const displayName = folderName.split('/').pop() || folderName;
            
            html += `
                <label class="filter-toggle" data-folder="${folderName}">
                    <input type="checkbox" checked style="display: none;">
                    <span class="filter-btn" style="background: var(--primary-color); color: white;">
                        <i class="fas fa-folder"></i> ${displayName}
                    </span>
                </label>
            `;
        });
        
        filtersContainer.innerHTML = html;
        
        // Add event listeners
        filtersContainer.querySelectorAll('.filter-toggle').forEach(filter => {
            filter.addEventListener('click', () => {
                const checkbox = filter.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
            });
        });
    },
    
    async updateStatistics() {
        try {
            const response = await fetch('/api/index-statistics');
            const data = await response.json();
            
            if (data.status === 'success') {
                const stats = data.statistics || {};
                
                document.getElementById('total-folders').textContent = stats.total_folders || 0;
                document.getElementById('total-files').textContent = stats.total_files || 0;
                document.getElementById('indexed-files').textContent = stats.indexed_files || 0;
                document.getElementById('index-status').textContent = stats.status || 'Idle';
            }
        } catch (error) {
            console.error('Error updating statistics:', error);
        }
    },
    
    async performIndexedSearch(e) {
        e.preventDefault();
        
        if (this.indexedFolders.length === 0) {
            this.showNotification('No indexed folders available. Please add folders to the index first.', 'warning');
            return;
        }
        
        const query = document.getElementById('indexed-search-query').value.trim();
        if (!query) {
            this.showNotification('Please enter a search query.', 'warning');
            return;
        }
        
        if (this.searchInProgress) {
            return;
        }
        
        this.searchInProgress = true;
        this.showIndexedSearchProgress();
        
        // Get selected folders
        const selectedFolders = [];
        document.querySelectorAll('#folder-filters .filter-toggle input:checked').forEach(checkbox => {
            selectedFolders.push(checkbox.parentElement.dataset.folder);
        });
        
        // Prepare search data
        const searchData = {
            query: query,
            folders: selectedFolders,
            advanced_options: {
                case_sensitive: document.getElementById('indexed-case-sensitive').checked,
                whole_word: document.getElementById('indexed-whole-word').checked,
                regex: document.getElementById('indexed-use-regex').checked,
                search_mode: document.getElementById('indexed-search-mode').value
            },
            document_types: this.getSelectedDocumentTypes()
        };
        
        try {
            const response = await fetch('/search-indexed', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(searchData)
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.currentSearchResults = data.results || [];
                this.displayIndexedSearchResults(this.currentSearchResults);
                this.showNotification(`Search completed! Found ${this.currentSearchResults.length} files with matches.`, 'success');
            } else {
                this.showNotification(`Search failed: ${data.message}`, 'danger');
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showNotification('An error occurred during search.', 'danger');
        } finally {
            this.searchInProgress = false;
            this.hideIndexedSearchProgress();
        }
    },
    
    getSelectedDocumentTypes() {
        const selectedTypes = [];
        document.querySelectorAll('#indexed-doc-type-filters .filter-toggle input:checked').forEach(checkbox => {
            const type = checkbox.parentElement.dataset.type;
            if (this.fileTypeMap[type]) {
                selectedTypes.push(...this.fileTypeMap[type]);
            }
        });
        return selectedTypes;
    },
    
    showIndexedSearchProgress() {
        const progressSection = document.getElementById('indexed-search-progress');
        progressSection.style.display = 'block';
        progressSection.classList.add('fade-in');
        
        const searchBtn = document.getElementById('indexed-search-btn');
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<div class="loading-spinner"></div> Searching...';
        
        // Simulate progress updates
        let progress = 0;
        const progressBar = document.getElementById('indexed-search-progress-bar');
        const statusSpan = document.getElementById('indexed-search-status');
        const matchesSpan = document.getElementById('indexed-matches-found');
        
        this.searchProgressInterval = setInterval(() => {
            if (!this.searchInProgress) {
                clearInterval(this.searchProgressInterval);
                return;
            }
            
            progress += Math.random() * 15;
            if (progress > 95) progress = 95;
            
            progressBar.style.width = progress + '%';
            statusSpan.textContent = 'Searching indexed files...';
        }, 300);
    },
    
    hideIndexedSearchProgress() {
        if (this.searchProgressInterval) {
            clearInterval(this.searchProgressInterval);
        }
        
        const searchBtn = document.getElementById('indexed-search-btn');
        searchBtn.disabled = this.indexedFolders.length === 0 || !document.getElementById('indexed-search-query').value.trim();
        searchBtn.innerHTML = '<i class="fas fa-search"></i> Search Index';
        
        // Complete progress bar
        const progressBar = document.getElementById('indexed-search-progress-bar');
        progressBar.style.width = '100%';
        document.getElementById('indexed-search-status').textContent = 'Search completed';
        
        setTimeout(() => {
            document.getElementById('indexed-search-progress').style.display = 'none';
        }, 2000);
    },
    
    displayIndexedSearchResults(results) {
        const resultsSection = document.getElementById('indexed-search-results');
        const resultsContainer = document.getElementById('indexed-results-container');
        const resultsCount = document.getElementById('indexed-results-count');
        
        resultsCount.textContent = results.length;
        resultsSection.style.display = 'block';
        resultsSection.classList.add('fade-in');
        
        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="alert alert-warning" style="text-align: center;">
                    <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h4>No matches found</h4>
                    <p>No matches found for "${document.getElementById('indexed-search-query').value}" in the indexed folders.</p>
                    <p style="font-size: 0.875rem; color: var(--secondary-color);">Try adjusting your search terms or re-indexing your folders.</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        results.forEach((result, index) => {
            const fileName = result.name || result.path.split('/').pop();
            const fileIcon = this.getFileIcon(result.doc_type || result.extension || 'txt');
            const matchCount = result.match_count || result.matches?.length || 0;
            
            html += `
                <div class="result-card slide-up" style="animation-delay: ${index * 0.1}s;">
                    <div class="result-header" onclick="this.parentElement.querySelector('.result-body').classList.toggle('expanded'); this.parentElement.querySelector('.result-body').style.display = this.parentElement.querySelector('.result-body').style.display === 'block' ? 'none' : 'block';">
                        <div class="result-title">
                            <i class="${fileIcon}" style="color: ${this.getFileIconColor(result.doc_type || result.extension)};"></i>
                            <div>
                                <div style="font-weight: 600; margin-bottom: 0.25rem;">${fileName}</div>
                                <div style="font-size: 0.875rem; color: var(--secondary-color);">${result.path}</div>
                            </div>
                            <span class="result-badge">${matchCount} matches</span>
                        </div>
                        <div class="result-actions" onclick="event.stopPropagation();">
                            <button class="btn btn-outline btn-sm" onclick="window.indexingApp.viewFile('${result.path}')" title="View File">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="window.indexingApp.downloadFile('${result.path}')" title="Download File">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-outline btn-sm" onclick="window.indexingApp.toggleIndexedResultExpansion(this)" title="Toggle Matches">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                    <div class="result-body">
                        ${this.renderMatches(result.matches || [], document.getElementById('indexed-search-query').value)}
                    </div>
                </div>
            `;
        });
        
        resultsContainer.innerHTML = html;
        
        // Scroll to results
        setTimeout(() => {
            resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }, 300);
    },
    
    // Reuse methods from DirectSearch for file operations and result rendering
    getFileIcon(extension) {
        const iconMap = {
            pdf: 'fas fa-file-pdf',
            doc: 'fas fa-file-word',
            docx: 'fas fa-file-word',
            xls: 'fas fa-file-excel',
            xlsx: 'fas fa-file-excel',
            csv: 'fas fa-file-excel',
            ppt: 'fas fa-file-powerpoint',
            pptx: 'fas fa-file-powerpoint',
            jpg: 'fas fa-file-image',
            jpeg: 'fas fa-file-image',
            png: 'fas fa-file-image',
            gif: 'fas fa-file-image',
            bmp: 'fas fa-file-image',
            tiff: 'fas fa-file-image',
            tif: 'fas fa-file-image',
            txt: 'fas fa-file-alt',
            md: 'fas fa-file-alt',
            log: 'fas fa-file-alt'
        };
        
        return iconMap[extension?.toLowerCase()] || 'fas fa-file';
    },
    
    getFileIconColor(extension) {
        const colorMap = {
            pdf: '#dc2626',
            doc: '#2563eb',
            docx: '#2563eb',
            xls: '#059669',
            xlsx: '#059669',
            csv: '#059669',
            ppt: '#d97706',
            pptx: '#d97706',
            jpg: '#0891b2',
            jpeg: '#0891b2',
            png: '#0891b2',
            gif: '#0891b2',
            bmp: '#0891b2',
            tiff: '#0891b2',
            tif: '#0891b2',
            txt: '#64748b',
            md: '#64748b',
            log: '#64748b'
        };
        
        return colorMap[extension?.toLowerCase()] || '#64748b';
    },
    
    renderMatches(matches, searchTerm) {
        if (!matches || matches.length === 0) {
            return `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No specific match details available.
                </div>
            `;
        }
        
        let html = '';
        matches.forEach((match, index) => {
            const highlightedContext = this.highlightSearchTerms(match.context || match.text || '', searchTerm);
            
            html += `
                <div class="match-item" style="animation: slideUp 0.3s ease-out ${index * 0.05}s both;">
                    <div class="match-line">
                        <i class="fas fa-map-marker-alt" style="color: var(--primary-color);"></i>
                        Line ${match.line || match.line_number || 'Unknown'}
                        ${match.page ? `| Page ${match.page}` : ''}
                        ${match.confidence ? `| Confidence: ${Math.round(match.confidence * 100)}%` : ''}
                    </div>
                    <div class="match-context">${highlightedContext}</div>
                </div>
            `;
        });
        
        return html;
    },
    
    highlightSearchTerms(text, searchTerm) {
        if (!text || !searchTerm) return text;
        
        const escapedTerm = searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        const regex = new RegExp(`(${escapedTerm})`, 'gi');
        return text.replace(regex, '<span class="match-highlight">$1</span>');
    }
});
</script>
