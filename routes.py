"""
Routes for TextFinder Application.
Defines all the Flask routes for the application.
"""
import os
import logging
import threading
import string
import re
import platform
import subprocess
import traceback
from datetime import datetime
from flask import render_template, request, redirect, url_for, jsonify, flash, send_from_directory, send_file
from werkzeug.utils import secure_filename

from config_manager import config_manager
from cache_manager import cache_manager
from pdf_processor import pdf_processor
from search_engine import search_engine
from models import db, File, FileContent, SearchHistory
from utils import allowed_file, get_file_metadata

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("textfinder.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("textfinder.routes")

# Global variables
indexing_thread = None

def register_routes(app):
    """Register routes with the Flask application."""

    # Add utility functions for templates
    @app.context_processor
    def utility_processor():
        return {
            'now': datetime.now
        }

    @app.route('/')
    def index():
        """Show the homepage with search functionality and statistics."""
        try:
            # Get file statistics
            file_count = File.query.count()
            indexed_count = File.query.filter_by(indexed=True).count()

            # Get popular searches (top 5)
            popular_searches = SearchHistory.query.order_by(SearchHistory.results_count.desc()).limit(5).all()
        except Exception as e:
            logger.error(f"Database error in homepage: {str(e)}")
            # Provide default values if database is not available
            file_count = 0
            indexed_count = 0
            popular_searches = []

        # Check if indexing is in progress
        is_indexing = indexing_thread and indexing_thread.is_alive()

        return render_template('index.html',
                             file_count=file_count,
                             indexed_count=indexed_count,
                             popular_searches=popular_searches,
                             is_indexing=is_indexing)

    @app.route('/search')
    def search():
        """Search for files."""
        query = request.args.get('query', '')
        file_types = request.args.getlist('file_types')
        directory = request.args.get('directory', '')

        if not query:
            return render_template('search.html', query='', results=[])

        # Perform the search
        results = search_engine.search_files(
            query,
            [],  # Empty list for now, will be populated by the search engine
            ocr_options={}
        )

        return render_template('search.html', query=query, results=results)

    @app.route('/browse')
    def browse():
        """Browse indexed files."""
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # Get files with pagination
        files = File.query.order_by(File.filename).paginate(
            page=page, per_page=per_page, error_out=False
        )

        return render_template('browse.html', files=files)

    @app.route('/file/<int:file_id>')
    def file_details(file_id):
        """Show file details."""
        file = File.query.get_or_404(file_id)

        return render_template('file_details.html', file=file)

    @app.route('/preview/<int:file_id>')
    def preview_file(file_id):
        """Preview a file."""
        file = File.query.get_or_404(file_id)

        # Get file content
        contents = FileContent.query.filter_by(file_id=file_id).all()

        return render_template('preview.html', file=file, contents=contents)

    @app.route('/settings')
    def settings():
        """Show settings page."""
        return render_template('settings.html', os=os)

    @app.route('/index-directory', methods=['POST'])
    def index_directory():
        """Index a directory."""
        directory = request.form.get('directory', '')
        recursive = request.form.get('recursive', 'true') == 'true'

        if not directory:
            flash('Please provide a directory path.', 'error')
            return redirect(url_for('settings'))

        # Check if directory exists
        if not os.path.isdir(directory):
            try:
                # Try to create the directory
                os.makedirs(directory, exist_ok=True)
                flash(f'Created directory: {directory}', 'info')
            except Exception as e:
                flash(f'Error creating directory: {str(e)}', 'error')
                return redirect(url_for('settings'))

        # Start indexing in a background thread
        def start_indexing():
            with app.app_context():
                try:
                    # TODO: Implement indexing functionality
                    pass
                except Exception as e:
                    logger.error(f"Indexing error: {str(e)}")

        # Access the global variable
        global indexing_thread
        if indexing_thread and indexing_thread.is_alive():
            flash('Indexing is already in progress.', 'warning')
        else:
            indexing_thread = threading.Thread(target=start_indexing)
            indexing_thread.daemon = True
            indexing_thread.start()
            flash(f'Started indexing directory: {directory}', 'success')

        return redirect(url_for('settings'))

    @app.route('/indexing-status')
    def indexing_status():
        """Get the current indexing status."""
        return jsonify({
            'is_indexing': False,  # TODO: Implement indexing status
            'progress': {}
        })

    @app.route('/upload', methods=['GET', 'POST'])
    def upload_file():
        """Upload a file."""
        if request.method == 'POST':
            logger.info("Processing file upload request")

            # Check if a file was uploaded
            if 'file' not in request.files:
                logger.error("No file part in the request")
                flash('No file part', 'error')
                return redirect(request.url)

            file = request.files['file']
            logger.info(f"File received: {file.filename}")

            # Check if a file was selected
            if file.filename == '':
                logger.error("No selected file")
                flash('No selected file', 'error')
                return redirect(request.url)

            # Check if the file is allowed
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                logger.info(f"Secured filename: {filename}")

                # Ensure upload folder exists
                upload_folder = os.path.abspath("uploads")
                os.makedirs(upload_folder, exist_ok=True)
                logger.info(f"Upload folder: {upload_folder}")

                file_path = os.path.join(upload_folder, filename)
                logger.info(f"File will be saved to: {file_path}")

                # Save the file
                try:
                    file.save(file_path)
                    logger.info(f"File saved successfully to {file_path}")

                    # Verify file exists
                    if not os.path.exists(file_path):
                        logger.error(f"File was not saved properly: {file_path}")
                        flash(f'Error: File was not saved properly.', 'error')
                        return redirect(request.url)

                    logger.info(f"File size: {os.path.getsize(file_path)} bytes")

                    # TODO: Index the file

                    flash(f'File {filename} uploaded successfully.', 'success')
                    return redirect(url_for('browse'))
                except Exception as e:
                    logger.error(f"Error saving file: {str(e)}", exc_info=True)
                    flash(f'Error saving file: {str(e)}', 'error')
                    return redirect(request.url)
            else:
                logger.error(f"File type not allowed: {file.filename}")
                flash('File type not allowed.', 'error')
                return redirect(request.url)

        return render_template('upload.html')

    @app.route('/export-results', methods=['POST'])
    def export_results():
        """Export search results."""
        query = request.form.get('query', '')
        file_types = request.form.getlist('file_types')
        directory = request.form.get('directory', '')
        export_format = request.form.get('format', 'csv')

        if not query:
            flash('No search query provided.', 'error')
            return redirect(url_for('search'))

        # TODO: Implement export functionality

        flash('Export functionality not yet implemented.', 'warning')
        return redirect(url_for('search', query=query))

    @app.route('/download/<int:file_id>')
    def download_file(file_id):
        """Download a file."""
        file = File.query.get_or_404(file_id)

        directory = os.path.dirname(file.filepath)
        filename = os.path.basename(file.filepath)

        return send_from_directory(directory, filename, as_attachment=True)

    @app.route('/direct-search')
    def direct_search():
        """Direct file search without indexing."""
        return render_template('direct_search.html')

    @app.route('/indexing')
    def indexing():
        """File indexing for faster searching."""
        return render_template('indexing.html')

    @app.route('/test-bootstrap')
    def test_bootstrap():
        """Test page to verify Bootstrap 5 styling is working."""
        return render_template('test_bootstrap.html')

    @app.route('/browse-directory', methods=['POST'])
    def browse_directory():
        """Browse directories for selection."""
        # Get data from request (support both form and JSON)
        if request.is_json:
            data = request.get_json()
            parent_dir = data.get('parent_dir', '')
        else:
            parent_dir = request.form.get('parent_dir', '')

        # Default to root directories if no parent directory is provided
        if not parent_dir:
            if os.name == 'nt':  # Windows
                # List available drives
                drives = []
                bitmask = 0
                try:
                    from ctypes import windll
                    bitmask = windll.kernel32.GetLogicalDrives()
                except:
                    # Fallback if ctypes is not available
                    for letter in string.ascii_uppercase:
                        if os.path.exists(f"{letter}:\\"):
                            drives.append(f"{letter}:\\")

                if bitmask:
                    for letter in string.ascii_uppercase:
                        if bitmask & 1:
                            drives.append(f"{letter}:\\")
                        bitmask >>= 1

                return jsonify({
                    'status': 'success',
                    'directories': drives,
                    'parent': ''
                })
            else:  # Unix-like
                parent_dir = '/'
                # Continue with the code below to list directories in '/'

        # Check if the parent directory exists
        if not os.path.isdir(parent_dir):
            return jsonify({
                'status': 'error',
                'message': f'Directory not found: {parent_dir}'
            })

        try:
            # List subdirectories with hierarchy information
            subdirs = []
            for item in os.listdir(parent_dir):
                item_path = os.path.join(parent_dir, item)
                if os.path.isdir(item_path):
                    # Get subdirectory info
                    subdir_info = {
                        'path': item_path,
                        'name': item,
                        'has_subdirs': False
                    }

                    # Check if this directory has subdirectories
                    try:
                        for subitem in os.listdir(item_path):
                            subitem_path = os.path.join(item_path, subitem)
                            if os.path.isdir(subitem_path):
                                subdir_info['has_subdirs'] = True
                                break
                    except:
                        # Ignore errors when checking for subdirectories
                        pass

                    subdirs.append(subdir_info)

            return jsonify({
                'status': 'success',
                'directories': [d['path'] for d in subdirs],  # Keep backward compatibility
                'directory_info': subdirs,  # New field with detailed info
                'parent': parent_dir
            })
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'Error browsing directory: {str(e)}'
            })

    @app.route('/scan-directory', methods=['POST'])
    def scan_directory():
        """Scan a directory for files."""
        directory = request.form.get('directory', '')

        if not directory:
            return jsonify({
                'status': 'error',
                'message': 'No directory provided'
            })

        if not os.path.isdir(directory):
            return jsonify({
                'status': 'error',
                'message': f'Directory not found: {directory}'
            })

        try:
            # Get all files in the directory
            files = []
            logger.info(f"Scanning directory: {directory}")

            # List all files in the directory
            all_files = []
            for root, _, filenames in os.walk(directory):
                for filename in filenames:
                    all_files.append(os.path.join(root, filename))

            logger.info(f"Found {len(all_files)} total files in {directory}")

            # Process each file
            for file_path in all_files:
                try:
                    filename = os.path.basename(file_path)
                    file_ext = os.path.splitext(filename)[1].lower()

                    # Remove the dot from the extension
                    if file_ext.startswith('.'):
                        file_ext = file_ext[1:]

                    # Include supported document types
                    supported_extensions = [
                        # Text files
                        'txt', 'csv', 'md', 'py', 'js', 'html', 'css', 'json', 'xml', 'log',
                        # Documents
                        'pdf', 'docx', 'doc',
                        # Spreadsheets
                        'xlsx', 'xls',
                        # Presentations
                        'pptx', 'ppt',
                        # Images
                        'jpg', 'jpeg', 'png', 'tif', 'tiff', 'bmp', 'gif'
                    ]

                    if file_ext in supported_extensions:
                        file_size = os.path.getsize(file_path)

                        # Skip files that are too large (>50MB)
                        if file_size > 50 * 1024 * 1024:
                            logger.info(f"Skipping large file: {file_path} ({file_size} bytes)")
                            continue

                        # Determine document type category
                        doc_type = 'text'
                        if file_ext in ['pdf', 'docx', 'doc']:
                            doc_type = 'document'
                        elif file_ext in ['xlsx', 'xls', 'csv']:
                            doc_type = 'spreadsheet'
                        elif file_ext in ['pptx', 'ppt']:
                            doc_type = 'presentation'
                        elif file_ext in ['jpg', 'jpeg', 'png', 'tif', 'tiff', 'bmp', 'gif']:
                            doc_type = 'image'

                        files.append({
                            'path': file_path,
                            'name': filename,
                            'size': file_size,
                            'type': file_ext,
                            'doc_type': doc_type,
                            'is_pdf': file_ext.lower() == 'pdf'
                        })
                except Exception as e:
                    logger.error(f"Error processing file {file_path}: {str(e)}")
                    continue

            logger.info(f"Found {len(files)} text files in {directory}")

            # If no files were found, include all files regardless of extension (up to 100)
            if len(files) == 0:
                logger.info(f"No text files found, including all files")
                for file_path in all_files[:100]:  # Limit to 100 files
                    try:
                        filename = os.path.basename(file_path)
                        file_ext = os.path.splitext(filename)[1].lower()
                        if file_ext.startswith('.'):
                            file_ext = file_ext[1:]

                        file_size = os.path.getsize(file_path)

                        # Skip files that are too large (>10MB)
                        if file_size > 10 * 1024 * 1024:
                            continue

                        files.append({
                            'path': file_path,
                            'name': filename,
                            'size': file_size,
                            'type': file_ext or 'unknown',
                            'is_pdf': file_ext.lower() == 'pdf'
                        })
                    except Exception as e:
                        logger.error(f"Error processing file {file_path}: {str(e)}")
                        continue

            return jsonify({
                'status': 'success',
                'files': files,
                'count': len(files)
            })
        except Exception as e:
            logger.error(f"Error scanning directory {directory}: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'Error scanning directory: {str(e)}'
            })

    @app.route('/search-files', methods=['POST'])
    def search_files():
        """
        Search for text in files.
        """
        try:
            data = request.get_json()
            query = data.get('query', '')
            files = data.get('files', [])
            ocr_options = data.get('ocr_options', {})
            doc_types = data.get('doc_types', [])
            advanced_options = data.get('advanced_options', {})
            
            # Validate query
            if not query:
                return jsonify({'error': 'No query provided'}), 400
            
            # Validate and normalize advanced options
            if advanced_options:
                # Ensure boolean values are actually booleans
                for key in ['case_sensitive', 'whole_word', 'regex', 'multiple_keywords', 'is_advanced_search']:
                    if key in advanced_options:
                        advanced_options[key] = bool(advanced_options[key])
                    
                # Ensure search_mode is either 'AND' or 'OR'
                if 'search_mode' in advanced_options:
                    if advanced_options['search_mode'] not in ['AND', 'OR']:
                        advanced_options['search_mode'] = 'AND'
                    
                # Log the advanced options for debugging
                app.logger.info(f"Advanced search options: {advanced_options}")

            # Validate required fields
            if not files:
                return jsonify({'error': 'No files provided'}), 400

            # Log the request data for debugging
            logger.info(f"Search request: query='{query}', files_count={len(files)}, doc_types={doc_types}")

            # Extract file paths from file objects
            file_paths = []
            for i, file_item in enumerate(files):
                try:
                    if isinstance(file_item, dict) and 'path' in file_item:
                        file_path = file_item['path']
                        if file_path and isinstance(file_path, str):
                            file_paths.append(file_path)
                        else:
                            logger.warning(f"Invalid file path in item {i}: {file_path}")
                    elif isinstance(file_item, str):
                        if file_item:
                            file_paths.append(file_item)
                        else:
                            logger.warning(f"Empty file path string at index {i}")
                    else:
                        logger.warning(f"Skipping invalid file item at index {i}: {type(file_item)}")
                except Exception as item_error:
                    logger.warning(f"Error processing file item at index {i}: {str(item_error)}")
                    continue

            if not file_paths:
                return jsonify({'error': 'No valid file paths found'}), 400

            logger.info(f"Extracted {len(file_paths)} file paths")

            # Validate file paths
            valid_file_paths = []
            for file_path in file_paths:
                try:
                    if os.path.exists(file_path):
                        valid_file_paths.append(file_path)
                    else:
                        logger.warning(f"File not found: {file_path}")
                except Exception as path_error:
                    logger.warning(f"Error validating file path {file_path}: {str(path_error)}")
                    continue

            if not valid_file_paths:
                return jsonify({
                    'status': 'success',
                    'results': [],
                    'count': 0,
                    'message': 'No valid existing files found'
                })

            file_paths = valid_file_paths
            logger.info(f"Found {len(file_paths)} existing files")

            # Filter files by document type if specified
            if doc_types and len(doc_types) > 0:
                filtered_files = []
                for file_path in file_paths:
                    try:
                        file_ext = os.path.splitext(file_path)[1].lower()[1:]

                        # Check if file extension matches any of the selected document types
                        if ('pdf' in doc_types and file_ext == 'pdf') or \
                           ('docx' in doc_types and file_ext in ['docx', 'doc']) or \
                           ('xlsx' in doc_types and file_ext in ['xlsx', 'xls', 'csv']) or \
                           ('pptx' in doc_types and file_ext in ['pptx', 'ppt']) or \
                           ('image' in doc_types and file_ext in ['jpg', 'jpeg', 'png', 'tif', 'tiff', 'bmp', 'gif']) or \
                           ('text' in doc_types and file_ext in ['txt', 'md', 'py', 'js', 'html', 'css', 'json', 'xml', 'log']):
                            filtered_files.append(file_path)
                    except Exception as filter_error:
                        logger.warning(f"Error filtering file {file_path}: {str(filter_error)}")
                        continue

                file_paths = filtered_files
                logger.info(f"Filtered to {len(file_paths)} files based on document types")

            if not file_paths:
                return jsonify({
                    'status': 'success',
                    'results': [],
                    'count': 0,
                    'message': 'No files match the selected document types'
                })

            # Search files using the SearchEngine class
            logger.info(f"Searching for '{query}' in {len(file_paths)} files")
            try:
                # Parse the query with advanced options
                query_info = search_engine.parse_advanced_query(query, advanced_options)
                
                # Perform the search
                results = search_engine.search_files(
                    query_info, 
                    file_paths, 
                    advanced_options, 
                    ocr_options
                )
                logger.info(f"Search completed: Found {len(results)} results")

                # Return results
                return jsonify({
                    'status': 'success',
                    'results': results,
                    'count': len(results)
                })
            except Exception as search_error:
                logger.error(f"Error in search_engine.search_files: {str(search_error)}")
                return jsonify({
                    'status': 'error',
                    'message': f'Error searching files: {str(search_error)}'
                })

        except Exception as e:
            logger.error(f"Error in search_files: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return jsonify({
                'status': 'error',
                'message': f'Error processing search request: {str(e)}'
            })

    @app.route('/process-pdf', methods=['POST'])
    def process_pdf():
        """Process a PDF file to make it searchable."""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid JSON data'
                })

            file_path = data.get('file_path', '')
            output_path = data.get('output_path', '')
            ocr_language = data.get('ocr_language', 'eng')
            ocr_dpi = data.get('ocr_dpi', 300)
            enhance_contrast = data.get('enhance_contrast', True)

            if not file_path:
                return jsonify({
                    'status': 'error',
                    'message': 'No file path provided'
                })

            if not os.path.exists(file_path):
                return jsonify({
                    'status': 'error',
                    'message': f'File not found: {file_path}'
                })

            # Process the PDF
            result = pdf_processor.process_pdf(
                file_path,
                output_path,
                ocr_language,
                ocr_dpi,
                enhance_contrast
            )

            if result:
                return jsonify({
                    'status': 'success',
                    'output_path': result
                })
            else:
                return jsonify({
                    '极status': 'error',
                    'message': 'Failed to process PDF'
                })
        except Exception as e:
            logger.error(f"Error in process_pdf: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return jsonify({
                'status': 'error',
                'message': f'Error processing PDF: {str(e)}'
            })

    @app.route('/create-searchable-pdf', methods=['POST'])
    def create_searchable_pdf():
        """Create a searchable PDF file."""
        try:
            # Get JSON data from request
            try:
                data = request.get_json()
                if not data:
                    logger.warning("Invalid JSON data received in create-searchable-pdf request")
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid JSON data'
                    })
            except Exception as json_error:
                logger.error(f"Error parsing JSON in create-searchable-pdf request: {str(json_error)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid JSON format in request'
                })

            file_path = data.get('file_path', '')

            if not file_path:
                logger.warning("No file path provided in create-searchable-pdf request")
                return jsonify({
                    'status': 'error',
                    'message': 'No file path provided'
                })

            # Validate file path
            try:
                if not os.path.exists(file_path):
                    logger.warning(f"File not found in create-searchable-pdf request: {file_path}")
                    return jsonify({
                        'status': 'error',
                        'message': f'File not found: {file_path}'
                    })

                if not os.path.isfile(file_path):
                    logger.warning(f"Path is not a file in create-searchable-pdf request: {file_path}")
                    return jsonify({
                        'status': 'error',
                        'message': f'Path is not a file: {file_path}'
                    })
            except Exception as path_error:
                logger.error(f"Error validating file path in create-searchable-pdf request: {str(path_error)}")
                return jsonify({
                    'status': 'error',
                    'message': f'Error validating file path: {str(path_error)}'
                })

            # Get file extension
            try:
                file_ext = os.path.splitext(file_path)[1].lower()[1:]
                if file_ext != 'pdf':
                    logger.warning(f"Unsupported file type in create-searchable-pdf request: {file_ext}")
                    return jsonify({
                        'status': 'error',
                        'message': 'Only PDF files are supported'
                    })
            except Exception as ext_error:
                logger.error(f"Error getting file extension in create-searchable-pdf request: {str(ext_error)}")
                return jsonify({
                    'status': 'error',
                    'message': f'Error getting file extension: {str(ext_error)}'
                })

            # Create output path in the cache directory
            try:
                filename = os.path.basename(file_path)
                output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cache', 'searchable_pdfs')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, f"searchable_{filename}")
            except Exception as dir_error:
                logger.error(f"Error creating output directory in create-searchable-pdf request: {str(dir_error)}")
                return jsonify({
                    'status': 'error',
                    'message': f'Error creating output directory: {str(dir_error)}'
                })

            # Log the operation
            logger.info(f"Creating searchable PDF for: {file_path}")
            logger.info(f"Output path: {output_path}")

            # Process the PDF
            try:
                # Check if the file is actually a PDF
                if not file_path.lower().endswith('.pdf'):
                    logger.error(f"File is not a PDF: {file_path}")
                    return jsonify({
                        'status': 'error',
                        'message': 'Only PDF files are supported'
                    })

                # Check if the PDF processor is available
                if not hasattr(pdf_processor, 'make_pdf_selectable'):
                    logger.error("PDF processor does not have make_pdf_selectable method")
                    return jsonify({
                        'status': 'error',
                        'message': 'PDF processing functionality is not available'
                    })

                # Try to process the PDF
                result = pdf_processor.make_pdf_selectable(file_path, output_path)

                # Check if the result is valid
                if result and os.path.exists(output_path):
                    logger.info(f"Successfully created searchable PDF: {output_path}")
                else:
                    logger.error(f"Failed to create searchable PDF: {output_path}")
                    return jsonify({
                        'status': 'error',
                        'message': 'Failed to create searchable PDF'
                    })
            except Exception as pdf_error:
                logger.error(f"Error in pdf_processor.make_pdf_selectable: {str(pdf_error)}")
                logger.error(traceback.format_exc())
                return jsonify({
                    'status': 'error',
                    'message': f'Error processing PDF: {str(pdf_error)}'
                })

            if result:
                # Create a download URL
                download_url = f"/download-document?path={output_path}"
                logger.info(f"Successfully created searchable PDF: {output_path}")

                return jsonify({
                    'status': 'success',
                    'output_path': output_path,
                    'download_url': download_url
                })
            else:
                logger.error(f"Failed to create searchable PDF for: {file_path}")
                return jsonify({
                    'status': 'error',
                    'message': 'Failed to create searchable PDF'
                })
        except Exception as e:
            logger.error(f"Error in create_searchable_pdf: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return jsonify({
                'status': 'error',
                'message': f'Error creating searchable PDF: {str(e)}'
            })

    @app.route('/create-batch-searchable-pdfs', methods=['POST'])
    def create_batch_searchable_pdfs():
        """Create searchable PDFs for multiple files."""
        try:
            # Get JSON data from request
            try:
                data = request.get_json()
                if not data:
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid JSON data'
                    })
            except Exception as json_error:
                logger.error(f"Error parsing JSON: {str(json_error)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid JSON format in request'
                })

            file_paths = data.get('file_paths', [])

            if not file_paths:
                return jsonify({
                    'status': 'error',
                    'message': 'No file paths provided'
                })

            # Create output directory
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'cache', 'searchable_pdfs')
            os.makedirs(output_dir, exist_ok=True)

            # Process each PDF
            results = []
            for file_path in file_paths:
                try:
                    # Skip non-PDF files
                    if not file_path.lower().endswith('.pdf'):
                        results.append({
                            'file_path': file_path,
                            'status': 'error',
                            'message': 'Not a PDF file'
                        })
                        continue

                    # Skip non-existent files
                    if not os.path.exists(file_path):
                        results.append({
                            'file_path': file_path,
                            'status': 'error',
                            'message': 'File not found'
                        })
                        continue

                    # Create output path
                    filename = os.path.basename(file_path)
                    output_path = os.path.join(output_dir, f"searchable_{filename}")

                    # Process the PDF
                    logger.info(f"Creating searchable PDF for: {file_path}")
                    result = pdf_processor.make_pdf_selectable(file_path, output_path)

                    if result:
                        # Create a download URL
                        download_url = f"/download-document?path={output_path}"
                        logger.info(f"Successfully created searchable PDF: {output_path}")

                        results.append({
                            'file_path': file_path,
                            'status': 'success',
                            'output_path': output_path,
                            'download_url': download_url
                        })
                    else:
                        logger.error(f"Failed to create searchable PDF for: {file_path}")
                        results.append({
                            'file_path': file_path,
                            'status': 'error',
                            'message': 'Failed to create searchable PDF'
                        })
                except Exception as file_error:
                    logger.error(f"Error processing file {file_path}: {str(file_error)}")
                    results.append({
                        'file_path': file_path,
                        'status': 'error',
                        'message': str(file_error)
                    })

            return jsonify({
                'status': 'success',
                'results': results
            })
        except Exception as e:
            logger.error(f"Error in create_batch_searchable_pdfs: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return jsonify({
                'status': 'error',
                'message': f'Error creating batch searchable PDFs: {str(e)}'
            })

    @app.route('/processing-status')
    def processing_status():
        """Get the current PDF processing status."""
        return jsonify({
            'is_processing': pdf_processor.is_processing(),
            'progress': pdf_processor.get_progress()
        })

    @app.route('/open-pdf')
    def open_pdf():
        """Open a PDF file."""
        file_path = request.args.get('path', '')
        page = request.args.get('page', 1, type=int)

        if not file_path or not os.path.exists(file_path):
            flash('PDF file not found.', 'error')
            return redirect(url_for('direct_search'))

        # TODO: Implement PDF viewer

        return send_file(file_path)

    @app.route('/open-document')
    def open_document():
        """Open a document file."""
        file_path = request.args.get('path', '')
        page = request.args.get('page', 1, type=int)

        if not file_path or not os.path.exists(file_path):
            flash('Document file not found.', 'error')
            return redirect(url_for('direct_search'))

        # Get file extension
        file_ext = os.path.splitext(file_path)[1].lower()[1:]

        # For PDF files, redirect to open-pdf route
        if file_ext == 'pdf':
            return redirect(url_for('open_pdf', path=file_path, page=page))
        # For presentation files, redirect to open-presentation route
        elif file_ext in ['pptx', 'ppt']:
            return redirect(url_for('open_presentation', path=file_path, slide=page))
        # For image files, redirect to view-image route
        elif file_ext in ['jpg', 'jpeg', 'png', 'tif', 'tiff', 'bmp', 'gif']:
            return redirect(url_for('view_image', path=file_path))

        # For other document types, just send the file
        # The browser will handle opening it with the appropriate application
        return send_file(file_path)

    @app.route('/download-document')
    def download_document():
        """Download a document file with text layer."""
        file_path = request.args.get('path', '')

        if not file_path or not os.path.exists(file_path):
            flash('Document file not found.', 'error')
            return redirect(url_for('direct_search'))

        # Get file extension
        file_ext = os.path.splitext(file_path)[1].lower()[1:]
        filename = os.path.basename(file_path)

        # For PDF files with OCR, use the searchable version if available
        if file_ext == 'pdf':
            searchable_path = cache_manager.get_searchable_pdf_path(file_path)
            if searchable_path and os.path.exists(searchable_path):
                return send_file(searchable_path, as_attachment=True, download_name=f"searchable_{filename}")

        # For other document types, just send the original file
        return send_file(file_path, as_attachment=True, download_name=filename)

    @app.route('/open-presentation')
    def open_presentation():
        """Open a presentation file."""
        file_path = request.args.get('path', '')
        slide = request.args.get('slide', 1, type=int)

        if not file_path or not os.path.exists(file_path):
            flash('Presentation file not found.', 'error')
            return redirect(url_for('direct_search'))

        # For now, just send the file - the browser will handle opening it
        # In the future, we could implement a presentation viewer based on file type
        logger.info(f"Opening presentation {file_path}, slide {slide}")
        return send_file(file_path)

    @app.route('/view-image')
    def view_image():
        """View an image file."""
        file_path = request.args.get('path', '')

        if not file_path or not os.path.exists(file_path):
            flash('Image file not found.', 'error')
            return redirect(url_for('direct_search'))

        # For now, just send the file - the browser will handle displaying it
        # In the future, we could implement an image viewer with OCR text overlay
        logger.info(f"Viewing image {file_path}")
        return send_file(file_path)

    @app.route('/api/config')
    def get_config():
        """Get application configuration."""
        # Only return safe configuration values
        safe_config = {
            'app_name': config_manager.get('APP_NAME'),
            'app_version': config_manager.get('APP_VERSION'),
            'ocr_enabled': config_manager.get('OCR_ENABLED'),
            'ocr_language': config_manager.get('OCR_LANGUAGE'),
            'ocr_dpi': config_manager.get('OCR_DPI'),
            'max_search_results': config_manager.get('MAX_SEARCH_RESULTS'),
            'min_search_term_length': config_manager.get('MIN_SEARCH_TERM_LENGTH'),
            'allowed_extensions': list(config_manager.get('ALLOWED_EXTENSIONS', {}).keys())
        }

        return jsonify(safe_config)

    @app.route('/api/stats')
    def get_stats():
        """Get application statistics."""
        stats = {
            'total_files': File.query.count(),
            'indexed_files': File.query.filter_by(indexed=True).count(),
            'search_history': SearchHistory.query.count()
        }

        return jsonify(stats)

    @app.route('/api/indexed-folders')
    def get_indexed_folders():
        """Get the list of indexed folders."""
        try:
            # Get indexed folders from cache manager
            indexed_folders = cache_manager.get_indexed_folders()

            return jsonify({
                'status': 'success',
                'folders': indexed_folders
            })
        except Exception as e:
            logger.error(f"Error getting indexed folders: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'Error getting indexed folders: {str(e)}'
            })

    @app.route('/api/add-indexed-folder', methods=['POST'])
    def add_indexed_folder():
        """Add a folder to the index."""
        try:
            # Get JSON data from request
            try:
                data = request.get_json()
                if not data:
                    logger.warning("Invalid JSON data received in add-indexed-folder request")
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid JSON data'
                    })
            except Exception as json_error:
                logger.error(f"Error parsing JSON in add-indexed-folder request: {str(json_error)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid JSON format in request'
                })

            folder_path = data.get('folder_path', '')

            if not folder_path:
                logger.warning("No folder path provided in add-indexed-folder request")
                return jsonify({
                    'status': 'error',
                    'message': 'No folder path provided'
                })

            # Validate folder path
            try:
                if not os.path.exists(folder_path):
                    logger.warning(f"Folder not found in add-indexed-folder request: {folder_path}")
                    return jsonify({
                        'status': 'error',
                        'message': f'Folder not found: {folder_path}'
                    })

                if not os.path.isdir(folder_path):
                    logger.warning(f"Path is not a folder in add-indexed-folder request: {folder_path}")
                    return jsonify({
                        'status': 'error',
                        'message': f'Path is not a folder: {folder_path}'
                    })
            except Exception as path_error:
                logger.error(f"Error validating folder path in add-indexed-folder request: {str(path_error)}")
                return jsonify({
                    'status': 'error',
                    'message': f'Error validating folder path: {str(path_error)}'
                })

            # Add folder to index
            result = cache_manager.add_indexed_folder(folder_path)

            if result:
                logger.info(f"Added folder to index: {folder_path}")
                return jsonify({
                    'status': 'success',
                    'message': f'Folder added to index: {folder_path}'
                })
            else:
                logger.error(f"Failed to add folder to index: {folder_path}")
                return jsonify({
                    'status': 'error',
                    'message': f'Failed to add folder to index: {folder_path}'
                })
        except Exception as e:
            logger.error(f"Error in add_indexed_folder: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'Error adding folder to index: {str(e)}'
            })

    @app.route('/api/remove-indexed-folder', methods=['POST'])
    def remove_indexed_folder():
        """Remove a folder from the index."""
        try:
            # Get JSON data from request
            try:
                data = request.get_json()
                if not data:
                    logger.warning("Invalid JSON data received in remove-indexed-folder request")
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid JSON data'
                    })
            except Exception as json_error:
                logger.error(f"Error parsing JSON in remove-indexed-folder request: {str(json_error)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid JSON format in request'
                })

            folder_path = data.get('folder_path', '')

            if not folder_path:
                logger.warning("No folder path provided in remove-indexed-folder request")
                return jsonify({
                    'status': 'error',
                    'message': 'No folder path provided'
                })

            # Remove folder from index
            result = cache_manager.remove_indexed_folder(folder_path)

            if result:
                logger.info(f"Removed folder from index: {folder_path}")
                return jsonify({
                    'status': 'success',
                    'message': f'Folder removed from index: {folder_path}'
                })
            else:
                logger.error(f"Failed to remove folder from index: {folder_path}")
                return jsonify({
                    'status': 'error',
                    'message': f'Failed to remove folder from index: {folder_path}'
                })
        except Exception as e:
            logger.error(f"Error in remove_indexed_folder: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'Error removing folder from index: {str(e)}'
            })

    @app.route('/api/scan-indexed-folders', methods=['POST'])
    def scan_indexed_folders():
        """Scan all indexed folders."""
        try:
            # Get indexed folders
            indexed_folders = cache_manager.get_indexed_folders()

            if not indexed_folders:
                logger.warning("No indexed folders found in scan-indexed-folders request")
                return jsonify({
                    'status': 'success',
                    'message': 'No indexed folders found',
                    'folders': [],
                    'results': []
                })

            # Scan each folder
            results = []
            for folder in indexed_folders:
                try:
                    folder_path = folder.get('path', '')
                    if not folder_path or not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                        logger.warning(f"Invalid folder in scan-indexed-folders request: {folder_path}")
                        results.append({
                            'path': folder_path,
                            'status': 'error',
                            'message': 'Invalid folder path'
                        })
                        continue

                    # Scan the folder
                    scan_result = cache_manager.scan_folder(folder_path)

                    if scan_result:
                        logger.info(f"Scanned folder: {folder_path}")
                        results.append({
                            'path': folder_path,
                            'status': 'success',
                            'file_count': scan_result.get('file_count', 0),
                            'last_scanned': scan_result.get('last_scanned', '')
                        })
                    else:
                        logger.error(f"Failed to scan folder: {folder_path}")
                        results.append({
                            'path': folder_path,
                            'status': 'error',
                            'message': 'Failed to scan folder'
                        })
                except Exception as folder_error:
                    logger.error(f"Error scanning folder {folder.get('path', '')}: {str(folder_error)}")
                    results.append({
                        'path': folder.get('path', ''),
                        'status': 'error',
                        'message': f'Error scanning folder: {str(folder_error)}'
                    })
                    continue

            return jsonify({
                'status': 'success',
                'results': results
            })
        except Exception as e:
            logger.error(f"Error in scan_indexed_folders: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'Error scanning indexed folders: {str(e)}'
            })

    @app.route('/api/scan-single-folder', methods=['POST'])
    def scan_single_folder():
        """Scan a single folder."""
        try:
            # Get JSON data from request
            try:
                data = request.get_json()
                if not data:
                    logger.warning("Invalid JSON data received in scan-single-folder request")
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid JSON data'
                    })
            except Exception as json_error:
                logger.error(f"Error parsing JSON in scan-single-folder request: {str(json_error)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid JSON format in request'
                })

            folder_path = data.get('folder_path', '')

            if not folder_path:
                logger.warning("No folder path provided in scan-single-folder request")
                return jsonify({
                    'status': 'error',
                    'message': 'No folder path provided'
                })

            # Validate folder path
            try:
                if not os.path.exists(folder_path):
                    logger.warning(f"Folder not found in scan-single-folder request: {folder_path}")
                    return jsonify({
                        'status': 'error',
                        'message': f'Folder not found: {folder_path}'
                    })

                if not os.path.isdir(folder_path):
                    logger.warning(f"Path is not a folder in scan-single-folder request: {folder_path}")
                    return jsonify({
                        'status': 'error',
                        'message': f'Path is not a folder: {folder_path}'
                    })
            except Exception as path_error:
                logger.error(f"Error validating folder path in scan-single-folder request: {str(path_error)}")
                return jsonify({
                    'status': 'error',
                    'message': f'Error validating folder path: {str(path_error)}'
                })

            # Scan the folder
            scan_result = cache_manager.scan_folder(folder_path)

            if scan_result:
                logger.info(f"Scanned folder: {folder_path}")
                return jsonify({
                    'status': 'success',
                    'file_count': scan_result.get('file_count', 0),
                    'last_scanned': scan_result.get('last_scanned', '')
                })
            else:
                logger.error(f"Failed to scan folder: {folder_path}")
                return jsonify({
                    'status': 'error',
                    'message': f'Failed to scan folder: {folder_path}'
                })
        except Exception as e:
            logger.error(f"Error in scan_single_folder: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': f'Error scanning folder: {str(e)}'
            })

    @app.route('/api/search-indexed-folders', methods=['POST'])
    def search_indexed_folders():
        """
        Search for text in indexed folders.
        """
        try:
            data = request.get_json()
            query = data.get('query', '')
            folder_paths = data.get('folder_paths', [])
            doc_types = data.get('doc_types', [])
            advanced_options = data.get('advanced_options', {})
            
            # Validate query
            if not query:
                return jsonify({'status': 'error', 'message': 'No query provided'}), 400
            
            # Validate and normalize advanced options
            if advanced_options:
                # Ensure boolean values are actually booleans
                for key in ['case_sensitive', 'whole_word', 'regex', 'multiple_keywords', 'is_advanced_search']:
                    if key in advanced_options:
                        advanced_options[key] = bool(advanced_options[key])
                    
                # Ensure search_mode is either 'AND' or 'OR'
                if 'search_mode' in advanced_options:
                    if advanced_options['search_mode'] not in ['AND', 'OR']:
                        advanced_options['search_mode'] = 'AND'
            
                app.logger.info(f"Advanced search options: {advanced_options}")
            
            # Initialize search engine
            search_engine = SearchEngine()
            
            # Parse the query with advanced options
            query_info = search_engine.parse_advanced_query(query, advanced_options)
            
            # Get indexed files from the database
            indexed_files = []
            for folder_path in folder_paths:
                # Query the database for indexed files in this folder
                files = IndexedFile.query.filter(
                    IndexedFile.folder_path == folder_path
                ).all()
                indexed_files.extend(files)
            
            # Filter by document types if specified
            if doc_types:
                indexed_files = [f for f in indexed_files if f.file_type in doc_types]
            
            # Search the files
            results = []
            total_files = len(indexed_files)
            processed_files = 0
            
            for indexed_file in indexed_files:
                processed_files += 1
                
                # Update progress
                progress = int((processed_files / total_files) * 100)
                socketio.emit('search_progress', {
                    'progress': progress,
                    'current_file': indexed_file.file_path
                })
                
                # Get the file content from the database
                file_content = indexed_file.content
                
                # Match the text
                if search_engine.match_text(file_content, query_info):
                    results.append({
                        'file_path': indexed_file.file_path,
                        'file_type': indexed_file.file_type,
                        'last_modified': indexed_file.last_modified.isoformat() if indexed_file.last_modified else None,
                        'size': indexed_file.size
                    })
            
            return jsonify({
                'status': 'success',
                'count': len(results),
                'results': results,
                'query': query,
                'advanced_options': advanced_options
            })
        
        except Exception as e:
            app.logger.error(f"Error in search_indexed_folders: {str(e)}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    @app.route('/open-folder')
    def open_folder():
        """
        Open a folder in the file explorer.
        """
        try:
            folder_path = request.args.get('path', '')
            
            if not folder_path:
                return jsonify({'status': 'error', 'message': 'No folder path provided'}), 400
            
            # Check if the folder exists
            if not os.path.isdir(folder_path):
                return jsonify({'status': 'error', 'message': 'Folder does not exist'}), 404
            
            # Open the folder in the file explorer
            if platform.system() == 'Windows':
                os.startfile(folder_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', folder_path])
            else:  # Linux
                subprocess.call(['xdg-open', folder_path])
            
            return jsonify({'status': 'success'})
        
        except Exception as e:
            app.logger.error(f"Error opening folder: {str(e)}")
            return jsonify({'status': 'error', 'message': str(e)}), 500

    @app.errorhandler(404)
    def page_not_found(e):
        """Handle 404 errors."""
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        """Handle 500 errors."""
        return render_template('errors/500.html'), 500
