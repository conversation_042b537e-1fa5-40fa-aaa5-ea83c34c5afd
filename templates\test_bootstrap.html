{% extends "layout.html" %}

{% block title %}Bootstrap Test - TextFinder{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4 text-primary">
                <i class="fas fa-check-circle"></i> Bootstrap 5 Test Page
                <small class="text-muted">(v2.0 - <PERSON><PERSON>)</small>
            </h1>
            <p class="lead">This page tests if Bootstrap 5 styling is working correctly.</p>
        </div>
    </div>

    <!-- Test Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> Test Card 1
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">This is a test card with Bootstrap 5 styling.</p>
                    <button class="btn btn-primary">Primary Button</button>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-check"></i> Test Card 2
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Another test card with different styling.</p>
                    <button class="btn btn-success">Success Button</button>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Test Card 3
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text">A third test card with warning styling.</p>
                    <button class="btn btn-warning">Warning Button</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-form"></i> Test Form Elements
                    </h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="test-input" class="form-label">Test Input</label>
                                <input type="text" class="form-control" id="test-input" placeholder="Enter text...">
                            </div>
                            <div class="col-md-6">
                                <label for="test-select" class="form-label">Test Select</label>
                                <select class="form-select" id="test-select">
                                    <option selected>Choose...</option>
                                    <option value="1">Option 1</option>
                                    <option value="2">Option 2</option>
                                    <option value="3">Option 3</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test-checkbox">
                                    <label class="form-check-label" for="test-checkbox">
                                        Test Checkbox
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">Submit Test</button>
                                <button type="button" class="btn btn-secondary ms-2">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Alerts -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle"></i> <strong>Success!</strong> Bootstrap 5 styling is working correctly.
            </div>
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle"></i> <strong>Info:</strong> This is an informational alert.
            </div>
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle"></i> <strong>Warning:</strong> This is a warning alert.
            </div>
        </div>
    </div>

    <!-- Test Progress Bar -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i> Test Progress Bar
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 75%"></div>
                    </div>
                    <p>Animated progress bar at 75%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modal Button -->
    <div class="row mb-4">
        <div class="col-12">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
                <i class="fas fa-window-maximize"></i> Test Modal
            </button>
        </div>
    </div>
</div>

<!-- Test Modal -->
<div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModalLabel">
                    <i class="fas fa-test"></i> Test Modal
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This is a test modal to verify Bootstrap 5 JavaScript functionality is working.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Bootstrap test page loaded successfully!');
    console.log('Bootstrap version:', typeof bootstrap !== 'undefined' ? 'Bootstrap 5 loaded' : 'Bootstrap not loaded');
});
</script>
{% endblock %}
