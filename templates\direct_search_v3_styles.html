<style>
/* Additional styles specific to Direct Search v3 */

/* Filter Toggle Buttons */
.filter-toggle {
    cursor: pointer;
    user-select: none;
}

.filter-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
}

.filter-toggle input:not(:checked) + .filter-btn {
    background: var(--light-color) !important;
    color: var(--secondary-color) !important;
    border-color: var(--border-color);
}

.filter-toggle:hover .filter-btn {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* Custom Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem 0;
    font-weight: 500;
    user-select: none;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
    background: white;
}

.checkbox-label input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Progress Bar Animation */
.progress-container {
    position: relative;
    overflow: hidden;
}

.progress-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* File List Styles */
.file-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
    cursor: pointer;
}

.file-item:hover {
    background: var(--light-color);
}

.file-item:last-child {
    border-bottom: none;
}

.file-checkbox {
    margin-right: 1rem;
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.file-icon {
    margin-right: 1rem;
    font-size: 1.5rem;
    width: 24px;
    text-align: center;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--dark-color);
}

.file-path {
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

.file-meta {
    font-size: 0.75rem;
    color: var(--secondary-color);
    display: flex;
    gap: 1rem;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

/* Result Card Styles */
.result-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: var(--transition);
}

.result-card:hover {
    box-shadow: var(--shadow-md);
}

.result-header {
    padding: 1rem 1.5rem;
    background: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.result-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: var(--dark-color);
}

.result-badge {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.result-actions {
    display: flex;
    gap: 0.5rem;
}

.result-body {
    padding: 1.5rem;
    display: none;
}

.result-body.expanded {
    display: block;
}

.match-item {
    background: var(--light-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-color);
}

.match-item:last-child {
    margin-bottom: 0;
}

.match-line {
    font-size: 0.75rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.match-context {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    background: white;
    padding: 0.75rem;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.match-highlight {
    background: #fef08a;
    color: #92400e;
    padding: 0.125rem 0.25rem;
    border-radius: 2px;
    font-weight: 600;
}

/* Modal Styles */
.modal {
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-dialog {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to { 
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

.directory-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    color: var(--dark-color);
}

.directory-item:hover {
    background: var(--light-color);
    color: var(--dark-color);
    text-decoration: none;
}

.directory-item:last-child {
    border-bottom: none;
}

.directory-icon {
    margin-right: 1rem;
    font-size: 1.25rem;
    width: 20px;
    text-align: center;
}

.directory-name {
    flex: 1;
    font-weight: 500;
}

.directory-size {
    font-size: 0.75rem;
    color: var(--secondary-color);
}

/* Breadcrumb Styles */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.breadcrumb-link {
    color: var(--primary-color);
    text-decoration: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: var(--transition);
}

.breadcrumb-link:hover {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-dark);
    text-decoration: none;
}

.breadcrumb-separator {
    color: var(--secondary-color);
}

.breadcrumb-current {
    color: var(--dark-color);
    font-weight: 600;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Alert Styles */
.alert {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-success {
    background: #f0fdf4;
    border-left-color: var(--success-color);
    color: #166534;
}

.alert-info {
    background: #f0f9ff;
    border-left-color: var(--info-color);
    color: #0c4a6e;
}

.alert-warning {
    background: #fffbeb;
    border-left-color: var(--warning-color);
    color: #92400e;
}

.alert-danger {
    background: #fef2f2;
    border-left-color: var(--danger-color);
    color: #991b1b;
}

/* Responsive Design */
@media (max-width: 768px) {
    .col-8, .col-6, .col-4 {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .modal-dialog {
        width: 95%;
        margin: 1rem;
        top: 1rem;
        transform: translateX(-50%);
    }
    
    .file-meta {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .result-actions {
        width: 100%;
        justify-content: flex-end;
    }
}

/* Utility Classes */
.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}
</style>
