<script>
// Indexing v3.0 - Modern JavaScript Implementation
class IndexingApp {
    constructor() {
        this.selectedFolder = '';
        this.indexedFolders = [];
        this.searchInProgress = false;
        this.indexingInProgress = false;
        this.currentSearchResults = [];
        
        // File type mappings (same as DirectSearch)
        this.fileTypeMap = {
            pdf: ['pdf'],
            word: ['doc', 'docx'],
            excel: ['xls', 'xlsx', 'csv'],
            powerpoint: ['ppt', 'pptx'],
            images: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff', 'tif'],
            text: ['txt', 'md', 'log', 'json', 'xml', 'html', 'css', 'js', 'py', 'java', 'cpp', 'c', 'h']
        };
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupDocumentTypeFilters();
        this.loadIndexedFolders();
        this.updateStatistics();
        console.log('Indexing v3.0 initialized successfully!');
    }
    
    bindEvents() {
        // Folder management
        document.getElementById('browse-folder-btn').addEventListener('click', () => this.openFolderBrowser());
        document.getElementById('add-folder-btn').addEventListener('click', () => this.addFolderToIndex());
        document.getElementById('refresh-folders').addEventListener('click', () => this.loadIndexedFolders());
        document.getElementById('clear-index').addEventListener('click', () => this.clearIndex());
        
        // Search functionality
        document.getElementById('indexed-search-form').addEventListener('submit', (e) => this.performIndexedSearch(e));
        document.getElementById('indexed-search-query').addEventListener('input', () => this.updateIndexedSearchButton());
        
        // Advanced options toggle
        document.getElementById('toggle-indexed-advanced').addEventListener('click', () => this.toggleIndexedAdvancedOptions());
        
        // Index management buttons
        document.getElementById('reindex-all-btn').addEventListener('click', () => this.reindexAllFolders());
        document.getElementById('rebuild-index-btn').addEventListener('click', () => this.rebuildIndex());
        document.getElementById('optimize-index-btn').addEventListener('click', () => this.optimizeIndex());
        
        // Modal events
        document.getElementById('close-folder-modal').addEventListener('click', () => this.closeFolderModal());
        document.getElementById('cancel-folder-modal').addEventListener('click', () => this.closeFolderModal());
        document.getElementById('select-folder-btn').addEventListener('click', () => this.selectCurrentFolder());
        
        // Results actions
        document.getElementById('expand-all-indexed-results').addEventListener('click', () => this.expandAllIndexedResults());
        document.getElementById('collapse-all-indexed-results').addEventListener('click', () => this.collapseAllIndexedResults());
        
        // Close modal on backdrop click
        document.querySelector('#folder-browser-modal .modal-backdrop').addEventListener('click', () => this.closeFolderModal());
    }
    
    setupDocumentTypeFilters() {
        const filters = document.querySelectorAll('#indexed-doc-type-filters .filter-toggle');
        filters.forEach(filter => {
            filter.addEventListener('click', () => {
                const checkbox = filter.querySelector('input[type="checkbox"]');
                checkbox.checked = !checkbox.checked;
                this.applyIndexedDocumentTypeFilters();
            });
        });
    }
    
    async openFolderBrowser() {
        document.getElementById('folder-browser-modal').style.display = 'block';
        document.body.style.overflow = 'hidden';
        await this.loadFolderContents('/');
    }
    
    closeFolderModal() {
        document.getElementById('folder-browser-modal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }
    
    async loadFolderContents(path) {
        const breadcrumb = document.getElementById('folder-breadcrumb');
        const contents = document.getElementById('folder-contents');
        
        // Update breadcrumb
        this.updateFolderBreadcrumb(path);
        
        // Show loading
        contents.innerHTML = `
            <div style="text-align: center; padding: 2rem;">
                <div class="loading-spinner"></div>
                <p style="margin-top: 1rem; color: var(--secondary-color);">Loading folder contents...</p>
            </div>
        `;
        
        try {
            const response = await fetch('/browse-directory', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ path: path })
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.displayFolderContents(data.contents || data.directories || [], path);
            } else {
                contents.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message || 'Failed to load folder'}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading folder:', error);
            contents.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error loading folder contents.
                </div>
            `;
        }
    }
    
    updateFolderBreadcrumb(path) {
        const breadcrumb = document.getElementById('folder-breadcrumb');
        const parts = path.split('/').filter(part => part);
        
        let html = `
            <div class="breadcrumb">
                <div class="breadcrumb-item">
                    <a href="#" class="breadcrumb-link" data-path="/">
                        <i class="fas fa-home"></i> Root
                    </a>
                </div>
        `;
        
        let currentPath = '';
        parts.forEach((part, index) => {
            currentPath += '/' + part;
            html += '<span class="breadcrumb-separator">/</span>';
            
            if (index === parts.length - 1) {
                html += `
                    <div class="breadcrumb-item">
                        <span class="breadcrumb-current">${part}</span>
                    </div>
                `;
            } else {
                html += `
                    <div class="breadcrumb-item">
                        <a href="#" class="breadcrumb-link" data-path="${currentPath}">${part}</a>
                    </div>
                `;
            }
        });
        
        html += '</div>';
        breadcrumb.innerHTML = html;
        
        // Add click listeners
        breadcrumb.querySelectorAll('.breadcrumb-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.loadFolderContents(link.dataset.path);
            });
        });
    }
    
    displayFolderContents(contents, currentPath) {
        const contentsDiv = document.getElementById('folder-contents');
        const selectBtn = document.getElementById('select-folder-btn');
        
        if (!contents || contents.length === 0) {
            contentsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> This folder is empty.
                </div>
            `;
            selectBtn.disabled = false;
            selectBtn.onclick = () => this.selectFolder(currentPath);
            return;
        }
        
        let html = '';
        
        // Add parent directory link if not at root
        if (currentPath !== '/') {
            const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
            html += `
                <a href="#" class="directory-item" data-path="${parentPath}">
                    <i class="fas fa-level-up-alt directory-icon" style="color: var(--secondary-color);"></i>
                    <span class="directory-name">.. (Parent Directory)</span>
                </a>
            `;
        }
        
        // Handle different response formats
        let directories = [];
        if (Array.isArray(contents)) {
            directories = contents.map(name => ({ name, type: 'directory' }));
        } else if (contents.directories) {
            directories = contents.directories.map(name => ({ name, type: 'directory' }));
        }
        
        // Sort directories alphabetically
        directories.sort((a, b) => a.name.localeCompare(b.name));
        
        directories.forEach(item => {
            const itemPath = currentPath === '/' ? `/${item.name}` : `${currentPath}/${item.name}`;
            html += `
                <a href="#" class="directory-item" data-path="${itemPath}" data-type="directory">
                    <i class="fas fa-folder directory-icon" style="color: #f59e0b;"></i>
                    <span class="directory-name">${item.name}</span>
                </a>
            `;
        });
        
        contentsDiv.innerHTML = html;
        
        // Add click listeners
        contentsDiv.querySelectorAll('.directory-item').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                if (link.dataset.type === 'directory' || link.dataset.path !== currentPath) {
                    this.loadFolderContents(link.dataset.path);
                }
            });
        });
        
        // Enable select button
        selectBtn.disabled = false;
        selectBtn.onclick = () => this.selectFolder(currentPath);
    }
    
    selectFolder(path) {
        this.selectedFolder = path;
        document.getElementById('new-folder-input').value = path;
        document.getElementById('add-folder-btn').disabled = false;
        this.closeFolderModal();
        this.showNotification('Folder selected successfully!', 'success');
    }
    
    selectCurrentFolder() {
        const currentSpan = document.querySelector('.breadcrumb-current');
        if (currentSpan) {
            const fullPath = this.getCurrentFolderPathFromBreadcrumb();
            this.selectFolder(fullPath);
        } else {
            this.selectFolder('/');
        }
    }
    
    getCurrentFolderPathFromBreadcrumb() {
        const breadcrumbLinks = document.querySelectorAll('#folder-breadcrumb .breadcrumb-link');
        const currentSpan = document.querySelector('#folder-breadcrumb .breadcrumb-current');
        
        let path = '';
        breadcrumbLinks.forEach(link => {
            if (link.dataset.path !== '/') {
                path = link.dataset.path;
            }
        });
        
        if (currentSpan) {
            path += (path === '' ? '' : '/') + currentSpan.textContent;
        }
        
        return path || '/';
    }
    
    async addFolderToIndex() {
        if (!this.selectedFolder) {
            this.showNotification('Please select a folder first.', 'warning');
            return;
        }
        
        const addBtn = document.getElementById('add-folder-btn');
        const originalText = addBtn.innerHTML;
        
        addBtn.disabled = true;
        addBtn.innerHTML = '<div class="loading-spinner"></div> Adding...';
        
        try {
            const response = await fetch('/start-indexing', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `directory=${encodeURIComponent(this.selectedFolder)}`
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showNotification(`Started indexing folder: ${this.selectedFolder}`, 'success');
                this.loadIndexedFolders();
                this.updateStatistics();
                
                // Clear selection
                this.selectedFolder = '';
                document.getElementById('new-folder-input').value = '';
                addBtn.disabled = true;
            } else {
                this.showNotification(`Failed to add folder: ${data.message}`, 'danger');
            }
        } catch (error) {
            console.error('Error adding folder:', error);
            this.showNotification('Error adding folder to index.', 'danger');
        } finally {
            addBtn.innerHTML = originalText;
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} fade-in`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            box-shadow: var(--shadow-lg);
        `;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; font-size: 1.2rem; cursor: pointer;">×</button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    toggleIndexedAdvancedOptions() {
        const panel = document.getElementById('indexed-advanced-options');
        const chevron = document.getElementById('indexed-advanced-chevron');
        
        if (panel.style.display === 'none') {
            panel.style.display = 'block';
            panel.classList.add('slide-up');
            chevron.style.transform = 'rotate(180deg)';
        } else {
            panel.style.display = 'none';
            chevron.style.transform = 'rotate(0deg)';
        }
    }
    
    updateIndexedSearchButton() {
        const query = document.getElementById('indexed-search-query').value.trim();
        const searchBtn = document.getElementById('indexed-search-btn');
        searchBtn.disabled = this.indexedFolders.length === 0 || !query;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.indexingApp = new IndexingApp();
});
</script>
