{% extends "layout.html" %}

{% block title %}TextFinder - Indexing{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="fas fa-database"></i> File Indexing
                <small class="text-muted">(v2.0 - Rebuilt)</small>
            </h1>
            <p class="lead">Index multiple folders for faster searching across your document collection.</p>

            <!-- Cache Notice -->
            <div class="alert alert-info alert-dismissible fade show" role="alert" id="cache-notice">
                <i class="fas fa-info-circle"></i>
                <strong>New Design!</strong> If you see basic styling instead of this modern interface, please refresh your browser (Ctrl+F5 or Cmd+Shift+R) to clear the cache.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Folder Management Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-folder-plus"></i> Folder Management
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-folders">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button type="button" class="btn btn-outline-danger btn-sm" id="clear-index">
                            <i class="fas fa-trash"></i> Clear Index
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Add New Folder -->
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-folder"></i></span>
                                <input type="text" class="form-control" id="new-folder-input" placeholder="Enter folder path or browse..." readonly>
                                <button type="button" class="btn btn-primary" id="browse-folder-btn">
                                    <i class="fas fa-folder-open"></i> Browse
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-success w-100" id="add-folder-btn" disabled>
                                <i class="fas fa-plus"></i> Add to Index
                            </button>
                        </div>
                    </div>

                    <!-- Indexed Folders List -->
                    <div class="mb-3">
                        <h6 class="fw-bold">Indexed Folders</h6>
                        <div id="indexed-folders-container">
                            <div class="alert alert-info text-center" id="no-folders-message">
                                <i class="fas fa-info-circle"></i> No folders indexed yet. Add folders above to begin.
                            </div>
                        </div>
                    </div>

                    <!-- Index Statistics -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0" id="total-folders">0</h4>
                                    <small>Indexed Folders</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0" id="total-files">0</h4>
                                    <small>Total Files</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0" id="indexed-files">0</h4>
                                    <small>Indexed Files</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body text-center">
                                    <h4 class="mb-0" id="index-status">Idle</h4>
                                    <small>Status</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Interface Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-search"></i> Search Indexed Folders
                    </h5>
                </div>
                <div class="card-body">
                    <form id="indexed-search-form">
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="indexed-search-query" class="form-label">Search Query</label>
                                <input type="text" class="form-control" id="indexed-search-query" placeholder="Enter text to search for..." required>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100" id="indexed-search-btn" disabled>
                                    <i class="fas fa-search"></i> Search Index
                                </button>
                            </div>
                        </div>
                        
                        <!-- Folder Filter -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label fw-bold">Filter by Folders:</label>
                                <div id="folder-filters" class="d-flex flex-wrap gap-2">
                                    <span class="text-muted">No indexed folders available</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Document Type Filters -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label fw-bold">Document Type Filters:</label>
                                <div class="btn-group flex-wrap" role="group" id="indexed-doc-type-filters">
                                    <input type="checkbox" class="btn-check" id="indexed-filter-pdf" checked>
                                    <label class="btn btn-outline-danger" for="indexed-filter-pdf">
                                        <i class="fas fa-file-pdf"></i> PDF
                                    </label>
                                    
                                    <input type="checkbox" class="btn-check" id="indexed-filter-word" checked>
                                    <label class="btn btn-outline-primary" for="indexed-filter-word">
                                        <i class="fas fa-file-word"></i> Word
                                    </label>
                                    
                                    <input type="checkbox" class="btn-check" id="indexed-filter-excel" checked>
                                    <label class="btn btn-outline-success" for="indexed-filter-excel">
                                        <i class="fas fa-file-excel"></i> Excel
                                    </label>
                                    
                                    <input type="checkbox" class="btn-check" id="indexed-filter-powerpoint" checked>
                                    <label class="btn btn-outline-warning" for="indexed-filter-powerpoint">
                                        <i class="fas fa-file-powerpoint"></i> PowerPoint
                                    </label>
                                    
                                    <input type="checkbox" class="btn-check" id="indexed-filter-images" checked>
                                    <label class="btn btn-outline-info" for="indexed-filter-images">
                                        <i class="fas fa-file-image"></i> Images
                                    </label>
                                    
                                    <input type="checkbox" class="btn-check" id="indexed-filter-text" checked>
                                    <label class="btn btn-outline-secondary" for="indexed-filter-text">
                                        <i class="fas fa-file-alt"></i> Text
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Search Options -->
                        <div class="row">
                            <div class="col-12">
                                <button class="btn btn-outline-secondary btn-sm mb-3" type="button" data-bs-toggle="collapse" data-bs-target="#indexed-advanced-options" aria-expanded="false">
                                    <i class="fas fa-cog"></i> Advanced Options
                                </button>
                                
                                <div class="collapse" id="indexed-advanced-options">
                                    <div class="card card-body bg-light">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6 class="fw-bold">Search Options</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="indexed-case-sensitive">
                                                    <label class="form-check-label" for="indexed-case-sensitive">
                                                        Case Sensitive
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="indexed-whole-word">
                                                    <label class="form-check-label" for="indexed-whole-word">
                                                        Whole Word Only
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="indexed-use-regex">
                                                    <label class="form-check-label" for="indexed-use-regex">
                                                        Regular Expression
                                                    </label>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="indexed-search-mode" class="form-label">Multiple Keywords</label>
                                                    <select class="form-select" id="indexed-search-mode">
                                                        <option value="AND">All keywords (AND)</option>
                                                        <option value="OR">Any keyword (OR)</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="fw-bold">Index Management</h6>
                                                <div class="d-grid gap-2">
                                                    <button type="button" class="btn btn-outline-info" id="reindex-all-btn">
                                                        <i class="fas fa-sync"></i> Re-index All Folders
                                                    </button>
                                                    <button type="button" class="btn btn-outline-warning" id="rebuild-index-btn">
                                                        <i class="fas fa-hammer"></i> Rebuild Index
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" id="optimize-index-btn">
                                                        <i class="fas fa-compress-arrows-alt"></i> Optimize Index
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Indexing Progress -->
    <div id="indexing-progress" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-spinner fa-spin"></i> Indexing in Progress
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" id="indexing-progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Current Folder:</strong> <span id="current-indexing-folder">-</span></p>
                            <p class="mb-1"><strong>Current File:</strong> <span id="current-indexing-file">-</span></p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Progress:</strong> <span id="indexing-file-progress">0 / 0</span></p>
                            <p class="mb-1"><strong>Status:</strong> <span id="indexing-status">Initializing...</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Progress -->
    <div id="indexed-search-progress" class="row mb-4" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-spinner fa-spin"></i> Searching Index
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" id="indexed-search-progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div class="text-center">
                        <p class="mb-1"><strong>Status:</strong> <span id="indexed-search-status">Searching...</span></p>
                        <p class="mb-1"><strong>Matches Found:</strong> <span id="indexed-matches-found">0</span></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Results -->
    <div id="indexed-search-results" class="row" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> Search Results (<span id="indexed-results-count">0</span> files)
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="expand-all-indexed-results">
                            <i class="fas fa-expand"></i> Expand All
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="collapse-all-indexed-results">
                            <i class="fas fa-compress"></i> Collapse All
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="indexed-results-container">
                        <!-- Results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Folder Browser Modal -->
<div class="modal fade" id="folder-browser-modal" tabindex="-1" aria-labelledby="folderBrowserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="folderBrowserModalLabel">
                    <i class="fas fa-folder-open"></i> Browse Folder
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb" id="folder-breadcrumb">
                            <li class="breadcrumb-item active">Loading...</li>
                        </ol>
                    </nav>
                </div>
                <div id="folder-contents" class="list-group">
                    <div class="text-center p-3">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="select-folder-btn" disabled>Select Folder</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let selectedFolder = '';
    let indexedFolders = [];
    let searchInProgress = false;
    let indexingInProgress = false;
    let currentSearchResults = [];

    // DOM elements
    const newFolderInput = document.getElementById('new-folder-input');
    const browseFolderBtn = document.getElementById('browse-folder-btn');
    const addFolderBtn = document.getElementById('add-folder-btn');
    const refreshFoldersBtn = document.getElementById('refresh-folders');
    const clearIndexBtn = document.getElementById('clear-index');
    const indexedFoldersContainer = document.getElementById('indexed-folders-container');
    const noFoldersMessage = document.getElementById('no-folders-message');

    // Search elements
    const indexedSearchForm = document.getElementById('indexed-search-form');
    const indexedSearchQuery = document.getElementById('indexed-search-query');
    const indexedSearchBtn = document.getElementById('indexed-search-btn');
    const indexedSearchProgress = document.getElementById('indexed-search-progress');
    const indexedSearchResults = document.getElementById('indexed-search-results');

    // Statistics elements
    const totalFoldersSpan = document.getElementById('total-folders');
    const totalFilesSpan = document.getElementById('total-files');
    const indexedFilesSpan = document.getElementById('indexed-files');
    const indexStatusSpan = document.getElementById('index-status');

    // Modal
    const folderBrowserModal = new bootstrap.Modal(document.getElementById('folder-browser-modal'));

    // Document type filters
    const indexedDocTypeFilters = {
        pdf: document.getElementById('indexed-filter-pdf'),
        word: document.getElementById('indexed-filter-word'),
        excel: document.getElementById('indexed-filter-excel'),
        powerpoint: document.getElementById('indexed-filter-powerpoint'),
        images: document.getElementById('indexed-filter-images'),
        text: document.getElementById('indexed-filter-text')
    };

    // Event listeners
    browseFolderBtn.addEventListener('click', openFolderBrowser);
    addFolderBtn.addEventListener('click', addFolderToIndex);
    refreshFoldersBtn.addEventListener('click', loadIndexedFolders);
    clearIndexBtn.addEventListener('click', clearIndex);
    indexedSearchForm.addEventListener('submit', performIndexedSearch);

    // Index management buttons
    document.getElementById('reindex-all-btn').addEventListener('click', reindexAllFolders);
    document.getElementById('rebuild-index-btn').addEventListener('click', rebuildIndex);
    document.getElementById('optimize-index-btn').addEventListener('click', optimizeIndex);

    // Search query input
    indexedSearchQuery.addEventListener('input', function() {
        indexedSearchBtn.disabled = !this.value.trim();
    });

    // Initialize
    loadIndexedFolders();
    updateStatistics();

    // Folder browser functionality
    function openFolderBrowser() {
        folderBrowserModal.show();
        loadFolderContents('/');
    }

    function loadFolderContents(path) {
        const breadcrumb = document.getElementById('folder-breadcrumb');
        const contents = document.getElementById('folder-contents');

        // Update breadcrumb
        updateFolderBreadcrumb(path);

        // Show loading
        contents.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading folder contents...</p>
            </div>
        `;

        // Make AJAX request to browse directory
        fetch('/browse-directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ path: path })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                displayFolderContents(data.contents, path);
            } else {
                contents.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading folder:', error);
            contents.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error loading folder contents.
                </div>
            `;
        });
    }

    function updateFolderBreadcrumb(path) {
        const breadcrumb = document.getElementById('folder-breadcrumb');
        const parts = path.split('/').filter(part => part);

        let breadcrumbHTML = '<li class="breadcrumb-item"><a href="#" data-path="/">Root</a></li>';
        let currentPath = '';

        parts.forEach((part, index) => {
            currentPath += '/' + part;
            if (index === parts.length - 1) {
                breadcrumbHTML += `<li class="breadcrumb-item active">${part}</li>`;
            } else {
                breadcrumbHTML += `<li class="breadcrumb-item"><a href="#" data-path="${currentPath}">${part}</a></li>`;
            }
        });

        breadcrumb.innerHTML = breadcrumbHTML;

        // Add click listeners to breadcrumb links
        breadcrumb.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                loadFolderContents(link.dataset.path);
            });
        });
    }

    function displayFolderContents(contents, currentPath) {
        const contentsDiv = document.getElementById('folder-contents');
        const selectBtn = document.getElementById('select-folder-btn');

        if (!contents || contents.length === 0) {
            contentsDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> This folder is empty.
                </div>
            `;
            selectBtn.disabled = false;
            selectBtn.onclick = () => selectFolder(currentPath);
            return;
        }

        let html = '';

        // Add parent directory link if not at root
        if (currentPath !== '/') {
            const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
            html += `
                <a href="#" class="list-group-item list-group-item-action" data-path="${parentPath}">
                    <i class="fas fa-level-up-alt text-secondary"></i> .. (Parent Directory)
                </a>
            `;
        }

        // Filter to show only directories
        const directories = contents.filter(item => item.type === 'directory');

        // Sort directories alphabetically
        directories.sort((a, b) => a.name.localeCompare(b.name));

        directories.forEach(item => {
            const itemPath = currentPath === '/' ? `/${item.name}` : `${currentPath}/${item.name}`;

            html += `
                <a href="#" class="list-group-item list-group-item-action" data-path="${itemPath}" data-type="directory">
                    <i class="fas fa-folder text-warning"></i> ${item.name}
                </a>
            `;
        });

        if (directories.length === 0) {
            html += `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> No subdirectories found.
                </div>
            `;
        }

        contentsDiv.innerHTML = html;

        // Add click listeners
        contentsDiv.querySelectorAll('a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                if (link.dataset.type === 'directory') {
                    loadFolderContents(link.dataset.path);
                }
            });
        });

        // Enable select button
        selectBtn.disabled = false;
        selectBtn.onclick = () => selectFolder(currentPath);
    }

    function selectFolder(path) {
        selectedFolder = path;
        newFolderInput.value = path;
        addFolderBtn.disabled = false;
        folderBrowserModal.hide();
    }

    // Folder management functions
    function addFolderToIndex() {
        if (!selectedFolder) {
            alert('Please select a folder first.');
            return;
        }

        addFolderBtn.disabled = true;
        addFolderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

        fetch('/api/add-indexed-folder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ folder_path: selectedFolder })
        })
        .then(response => response.json())
        .then(data => {
            addFolderBtn.disabled = false;
            addFolderBtn.innerHTML = '<i class="fas fa-plus"></i> Add to Index';

            if (data.status === 'success') {
                newFolderInput.value = '';
                selectedFolder = '';
                addFolderBtn.disabled = true;
                loadIndexedFolders();
                updateStatistics();
                showAlert('Folder added to index successfully!', 'success');
            } else {
                showAlert(`Failed to add folder: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Error adding folder:', error);
            addFolderBtn.disabled = false;
            addFolderBtn.innerHTML = '<i class="fas fa-plus"></i> Add to Index';
            showAlert('An error occurred while adding the folder.', 'danger');
        });
    }

    function loadIndexedFolders() {
        refreshFoldersBtn.disabled = true;
        refreshFoldersBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

        fetch('/api/indexed-folders')
        .then(response => response.json())
        .then(data => {
            refreshFoldersBtn.disabled = false;
            refreshFoldersBtn.innerHTML = '<i class="fas fa-sync"></i> Refresh';

            if (data.status === 'success') {
                indexedFolders = data.folders || [];
                displayIndexedFolders(indexedFolders);
                updateFolderFilters(indexedFolders);
            } else {
                showAlert(`Failed to load indexed folders: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading indexed folders:', error);
            refreshFoldersBtn.disabled = false;
            refreshFoldersBtn.innerHTML = '<i class="fas fa-sync"></i> Refresh';
            showAlert('An error occurred while loading indexed folders.', 'danger');
        });
    }

    function displayIndexedFolders(folders) {
        if (!folders || folders.length === 0) {
            indexedFoldersContainer.innerHTML = `
                <div class="alert alert-info text-center" id="no-folders-message">
                    <i class="fas fa-info-circle"></i> No folders indexed yet. Add folders above to begin.
                </div>
            `;
            return;
        }

        let html = '<div class="list-group">';
        folders.forEach((folder, index) => {
            const folderName = folder.path.split('/').pop() || folder.path;
            const statusBadge = folder.status === 'indexed' ?
                '<span class="badge bg-success">Indexed</span>' :
                '<span class="badge bg-warning">Pending</span>';

            html += `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">
                            <i class="fas fa-folder text-warning"></i> ${folderName}
                        </h6>
                        <p class="mb-1 text-muted small">${folder.path}</p>
                        <small class="text-muted">
                            Files: ${folder.file_count || 0} |
                            Last indexed: ${folder.last_indexed ? new Date(folder.last_indexed).toLocaleString() : 'Never'}
                        </small>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        ${statusBadge}
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="reindexFolder('${folder.path}')">
                                <i class="fas fa-sync"></i>
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="removeFolderFromIndex('${folder.path}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        indexedFoldersContainer.innerHTML = html;
    }

    function updateFolderFilters(folders) {
        const folderFiltersContainer = document.getElementById('folder-filters');

        if (!folders || folders.length === 0) {
            folderFiltersContainer.innerHTML = '<span class="text-muted">No indexed folders available</span>';
            return;
        }

        let html = '';
        folders.forEach((folder, index) => {
            const folderName = folder.path.split('/').pop() || folder.path;
            html += `
                <div class="form-check form-check-inline">
                    <input class="form-check-input folder-filter" type="checkbox" id="folder-filter-${index}" value="${folder.path}" checked>
                    <label class="form-check-label" for="folder-filter-${index}">
                        <i class="fas fa-folder text-warning"></i> ${folderName}
                    </label>
                </div>
            `;
        });

        folderFiltersContainer.innerHTML = html;
    }

    function clearIndex() {
        if (!confirm('Are you sure you want to clear the entire index? This action cannot be undone.')) {
            return;
        }

        clearIndexBtn.disabled = true;
        clearIndexBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Clearing...';

        fetch('/api/clear-index', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            clearIndexBtn.disabled = false;
            clearIndexBtn.innerHTML = '<i class="fas fa-trash"></i> Clear Index';

            if (data.status === 'success') {
                loadIndexedFolders();
                updateStatistics();
                showAlert('Index cleared successfully!', 'success');
            } else {
                showAlert(`Failed to clear index: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Error clearing index:', error);
            clearIndexBtn.disabled = false;
            clearIndexBtn.innerHTML = '<i class="fas fa-trash"></i> Clear Index';
            showAlert('An error occurred while clearing the index.', 'danger');
        });
    }

    function updateStatistics() {
        fetch('/api/index-statistics')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                totalFoldersSpan.textContent = data.total_folders || 0;
                totalFilesSpan.textContent = data.total_files || 0;
                indexedFilesSpan.textContent = data.indexed_files || 0;
                indexStatusSpan.textContent = data.index_status || 'Idle';
            }
        })
        .catch(error => {
            console.error('Error loading statistics:', error);
        });
    }

    // Search functionality
    function performIndexedSearch(e) {
        e.preventDefault();

        if (!indexedSearchQuery.value.trim()) {
            alert('Please enter a search query.');
            return;
        }

        if (searchInProgress) {
            return;
        }

        searchInProgress = true;
        showIndexedSearchProgress();

        // Get selected folders
        const selectedFolders = Array.from(document.querySelectorAll('.folder-filter:checked')).map(cb => cb.value);

        // Get selected document types
        const selectedDocTypes = [];
        Object.entries(indexedDocTypeFilters).forEach(([type, checkbox]) => {
            if (checkbox.checked) {
                selectedDocTypes.push(type);
            }
        });

        // Prepare search data
        const searchData = {
            query: indexedSearchQuery.value.trim(),
            folders: selectedFolders,
            document_types: selectedDocTypes,
            advanced_options: {
                case_sensitive: document.getElementById('indexed-case-sensitive').checked,
                whole_word: document.getElementById('indexed-whole-word').checked,
                regex: document.getElementById('indexed-use-regex').checked,
                search_mode: document.getElementById('indexed-search-mode').value
            }
        };

        // Start search
        fetch('/api/search-indexed-folders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(searchData)
        })
        .then(response => response.json())
        .then(data => {
            searchInProgress = false;
            hideIndexedSearchProgress();

            if (data.status === 'success') {
                currentSearchResults = data.results || [];
                displayIndexedSearchResults(currentSearchResults);
            } else {
                alert(`Search failed: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            searchInProgress = false;
            hideIndexedSearchProgress();
            alert('An error occurred during search.');
        });
    }

    function showIndexedSearchProgress() {
        indexedSearchProgress.style.display = 'block';
        indexedSearchBtn.disabled = true;
        indexedSearchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';

        // Simulate progress updates
        let progress = 0;
        const progressBar = document.getElementById('indexed-search-progress-bar');
        const searchStatusSpan = document.getElementById('indexed-search-status');
        const matchesFoundSpan = document.getElementById('indexed-matches-found');

        const progressInterval = setInterval(() => {
            if (!searchInProgress) {
                clearInterval(progressInterval);
                return;
            }

            progress += Math.random() * 15;
            if (progress > 95) progress = 95;

            progressBar.style.width = progress + '%';
            progressBar.setAttribute('aria-valuenow', progress);

            searchStatusSpan.textContent = 'Searching indexed files...';
        }, 300);
    }

    function hideIndexedSearchProgress() {
        indexedSearchProgress.style.display = 'none';
        indexedSearchBtn.disabled = !indexedSearchQuery.value.trim();
        indexedSearchBtn.innerHTML = '<i class="fas fa-search"></i> Search Index';

        // Complete progress bar
        const progressBar = document.getElementById('indexed-search-progress-bar');
        progressBar.style.width = '100%';
        document.getElementById('indexed-search-status').textContent = 'Search completed';
    }

    function displayIndexedSearchResults(results) {
        const resultsContainer = document.getElementById('indexed-results-container');
        const resultsCount = document.getElementById('indexed-results-count');

        resultsCount.textContent = results.length;
        indexedSearchResults.style.display = 'block';

        if (results.length === 0) {
            resultsContainer.innerHTML = `
                <div class="alert alert-warning text-center">
                    <i class="fas fa-search"></i> No matches found for "${indexedSearchQuery.value}" in the indexed folders.
                </div>
            `;
            return;
        }

        let html = '';
        results.forEach((result, index) => {
            const fileName = result.name || result.path.split('/').pop();
            const fileIcon = getFileIcon(result.doc_type || 'txt');
            const folderName = result.folder || result.path.split('/').slice(0, -1).join('/');

            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h6 class="mb-0">
                                    <i class="${fileIcon}"></i> ${fileName}
                                    <span class="badge bg-primary ms-2">${result.match_count} matches</span>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-folder text-warning"></i> ${folderName}
                                </small>
                                <br>
                                <small class="text-muted">${result.path}</small>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary" onclick="viewFile('${result.path}')">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="downloadFile('${result.path}')">
                                        <i class="fas fa-download"></i> Download
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#indexed-matches-${index}">
                                        <i class="fas fa-chevron-down"></i> Matches
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="collapse" id="indexed-matches-${index}">
                        <div class="card-body">
                            ${result.matches.map(match => `
                                <div class="mb-2 p-2 bg-light rounded">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <small class="text-muted">Line ${match.line}:</small>
                                            <div class="mt-1">${highlightSearchTerms(match.context, indexedSearchQuery.value)}</div>
                                        </div>
                                        ${match.page_link ? `
                                            <a href="${match.page_link}" class="btn btn-sm btn-outline-primary ms-2" target="_blank">
                                                <i class="fas fa-external-link-alt"></i>
                                            </a>
                                        ` : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        });

        resultsContainer.innerHTML = html;
    }

    function getFileIcon(extension) {
        const iconMap = {
            pdf: 'fas fa-file-pdf text-danger',
            doc: 'fas fa-file-word text-primary',
            docx: 'fas fa-file-word text-primary',
            xls: 'fas fa-file-excel text-success',
            xlsx: 'fas fa-file-excel text-success',
            csv: 'fas fa-file-excel text-success',
            ppt: 'fas fa-file-powerpoint text-warning',
            pptx: 'fas fa-file-powerpoint text-warning',
            jpg: 'fas fa-file-image text-info',
            jpeg: 'fas fa-file-image text-info',
            png: 'fas fa-file-image text-info',
            gif: 'fas fa-file-image text-info',
            bmp: 'fas fa-file-image text-info',
            tiff: 'fas fa-file-image text-info',
            tif: 'fas fa-file-image text-info',
            txt: 'fas fa-file-alt text-secondary',
            md: 'fas fa-file-alt text-secondary',
            log: 'fas fa-file-alt text-secondary'
        };

        return iconMap[extension.toLowerCase()] || 'fas fa-file text-secondary';
    }

    function highlightSearchTerms(text, searchTerm) {
        if (!text || !searchTerm) return text;

        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    // Index management functions
    function reindexAllFolders() {
        if (!confirm('Are you sure you want to re-index all folders? This may take some time.')) {
            return;
        }

        showAlert('Re-indexing started. This may take some time...', 'info');

        fetch('/api/reindex-all-folders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('Re-indexing completed successfully!', 'success');
                updateStatistics();
            } else {
                showAlert(`Re-indexing failed: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Re-indexing error:', error);
            showAlert('An error occurred during re-indexing.', 'danger');
        });
    }

    function rebuildIndex() {
        if (!confirm('Are you sure you want to rebuild the entire index? This will clear all existing index data and rebuild from scratch.')) {
            return;
        }

        showAlert('Index rebuild started. This may take some time...', 'info');

        fetch('/api/rebuild-index', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('Index rebuilt successfully!', 'success');
                loadIndexedFolders();
                updateStatistics();
            } else {
                showAlert(`Index rebuild failed: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Index rebuild error:', error);
            showAlert('An error occurred during index rebuild.', 'danger');
        });
    }

    function optimizeIndex() {
        showAlert('Index optimization started...', 'info');

        fetch('/api/optimize-index', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('Index optimized successfully!', 'success');
                updateStatistics();
            } else {
                showAlert(`Index optimization failed: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Index optimization error:', error);
            showAlert('An error occurred during index optimization.', 'danger');
        });
    }

    // Global functions for folder management
    window.reindexFolder = function(folderPath) {
        if (!confirm(`Are you sure you want to re-index the folder: ${folderPath}?`)) {
            return;
        }

        fetch('/api/reindex-folder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ folder_path: folderPath })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('Folder re-indexed successfully!', 'success');
                loadIndexedFolders();
                updateStatistics();
            } else {
                showAlert(`Re-indexing failed: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Re-indexing error:', error);
            showAlert('An error occurred during re-indexing.', 'danger');
        });
    };

    window.removeFolderFromIndex = function(folderPath) {
        if (!confirm(`Are you sure you want to remove the folder from index: ${folderPath}?`)) {
            return;
        }

        fetch('/api/remove-indexed-folder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ folder_path: folderPath })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                showAlert('Folder removed from index successfully!', 'success');
                loadIndexedFolders();
                updateStatistics();
            } else {
                showAlert(`Failed to remove folder: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Remove folder error:', error);
            showAlert('An error occurred while removing the folder.', 'danger');
        });
    };

    // File operations
    window.viewFile = function(filePath) {
        window.open(`/open-document?path=${encodeURIComponent(filePath)}`, '_blank');
    };

    window.downloadFile = function(filePath) {
        window.open(`/download-document?path=${encodeURIComponent(filePath)}`, '_blank');
    };

    // Expand/Collapse all results
    document.getElementById('expand-all-indexed-results').addEventListener('click', function() {
        document.querySelectorAll('#indexed-results-container .collapse').forEach(collapse => {
            new bootstrap.Collapse(collapse, { show: true });
        });
    });

    document.getElementById('collapse-all-indexed-results').addEventListener('click', function() {
        document.querySelectorAll('#indexed-results-container .collapse.show').forEach(collapse => {
            new bootstrap.Collapse(collapse, { hide: true });
        });
    });

    // Utility function to show alerts
    function showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.top = '20px';
        alertDiv.style.right = '20px';
        alertDiv.style.zIndex = '9999';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
});
</script>
{% endblock %}
