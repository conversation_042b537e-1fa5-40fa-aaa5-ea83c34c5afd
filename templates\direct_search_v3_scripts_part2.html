<script>
// Direct Search v3.0 - Additional JavaScript Functions (Part 2)

// Extend the DirectSearchApp class with additional methods
Object.assign(DirectSearchApp.prototype, {
    
    displayFileList(files) {
        const container = document.getElementById('file-list-container');
        
        if (!files || files.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 2rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: var(--warning-color); margin-bottom: 1rem;"></i>
                    <p style="color: var(--secondary-color); margin: 0;">No supported files found in the selected directory.</p>
                </div>
            `;
            return;
        }
        
        // Apply document type filters
        const filteredFiles = this.applyDocumentTypeFilters(files);
        
        let html = `
            <div style="margin-bottom: 1rem; padding: 1rem; background: var(--light-color); border-radius: var(--border-radius);">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span><strong>Files found:</strong> ${filteredFiles.length} of ${files.length}</span>
                    <span style="font-size: 0.875rem; color: var(--secondary-color);">Select files to search through</span>
                </div>
            </div>
            <div style="border: 1px solid var(--border-color); border-radius: var(--border-radius); overflow: hidden;">
        `;
        
        filteredFiles.forEach((file, index) => {
            const fileIcon = this.getFileIcon(file.extension);
            const fileSize = this.formatFileSize(file.size || 0);
            
            html += `
                <div class="file-item" data-file-path="${file.path}">
                    <input type="checkbox" class="file-checkbox" data-file-index="${index}" data-file-path="${file.path}">
                    <i class="${fileIcon} file-icon"></i>
                    <div class="file-info">
                        <div class="file-name">${file.name}</div>
                        <div class="file-path">${file.path}</div>
                        <div class="file-meta">
                            <span><strong>Type:</strong> ${file.extension.toUpperCase()}</span>
                            <span><strong>Size:</strong> ${fileSize}</span>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-outline btn-sm" onclick="window.directSearchApp.viewFile('${file.path}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline btn-sm" onclick="window.directSearchApp.downloadFile('${file.path}')">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
        
        // Enable selection buttons
        document.getElementById('select-all-files').disabled = false;
        document.getElementById('clear-selection').disabled = false;
        
        // Add event listeners to checkboxes
        const checkboxes = container.querySelectorAll('.file-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => this.updateSelectedFiles());
        });
        
        this.updateSelectedFiles();
    },
    
    getFileIcon(extension) {
        const iconMap = {
            pdf: 'fas fa-file-pdf',
            doc: 'fas fa-file-word',
            docx: 'fas fa-file-word',
            xls: 'fas fa-file-excel',
            xlsx: 'fas fa-file-excel',
            csv: 'fas fa-file-excel',
            ppt: 'fas fa-file-powerpoint',
            pptx: 'fas fa-file-powerpoint',
            jpg: 'fas fa-file-image',
            jpeg: 'fas fa-file-image',
            png: 'fas fa-file-image',
            gif: 'fas fa-file-image',
            bmp: 'fas fa-file-image',
            tiff: 'fas fa-file-image',
            tif: 'fas fa-file-image',
            txt: 'fas fa-file-alt',
            md: 'fas fa-file-alt',
            log: 'fas fa-file-alt'
        };
        
        return iconMap[extension.toLowerCase()] || 'fas fa-file';
    },
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    applyDocumentTypeFilters(files = this.scannedFiles) {
        const enabledTypes = [];
        
        document.querySelectorAll('.filter-toggle input:checked').forEach(checkbox => {
            const type = checkbox.parentElement.dataset.type;
            if (this.fileTypeMap[type]) {
                enabledTypes.push(...this.fileTypeMap[type]);
            }
        });
        
        const filtered = files.filter(file => 
            enabledTypes.includes(file.extension.toLowerCase())
        );
        
        if (files === this.scannedFiles && this.scannedFiles.length > 0) {
            this.displayFileList(filtered);
        }
        
        return filtered;
    },
    
    updateSelectedFiles() {
        const checkboxes = document.querySelectorAll('.file-checkbox:checked');
        this.selectedFiles = Array.from(checkboxes).map(cb => cb.dataset.filePath);
        this.updateSearchButton();
    },
    
    selectAllFiles() {
        const checkboxes = document.querySelectorAll('.file-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = true);
        this.updateSelectedFiles();
    },
    
    clearSelection() {
        const checkboxes = document.querySelectorAll('.file-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = false);
        this.updateSelectedFiles();
    },
    
    async performSearch(e) {
        e.preventDefault();
        
        if (this.selectedFiles.length === 0) {
            this.showNotification('Please select at least one file to search.', 'warning');
            return;
        }
        
        const query = document.getElementById('search-query').value.trim();
        if (!query) {
            this.showNotification('Please enter a search query.', 'warning');
            return;
        }
        
        if (this.searchInProgress) {
            return;
        }
        
        this.searchInProgress = true;
        this.showSearchProgress();
        
        // Prepare search data
        const searchData = {
            query: query,
            files: this.selectedFiles,
            advanced_options: {
                case_sensitive: document.getElementById('case-sensitive').checked,
                whole_word: document.getElementById('whole-word').checked,
                regex: document.getElementById('use-regex').checked,
                search_mode: document.getElementById('search-mode').value
            },
            ocr_options: {
                enabled: document.getElementById('enable-ocr').checked,
                handwriting: document.getElementById('handwriting-recognition').checked,
                language: document.getElementById('ocr-language').value,
                dpi: parseInt(document.getElementById('ocr-dpi').value)
            }
        };
        
        try {
            const response = await fetch('/search-files', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(searchData)
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.currentSearchResults = data.results || [];
                this.displaySearchResults(this.currentSearchResults);
                this.showNotification(`Search completed! Found ${this.currentSearchResults.length} files with matches.`, 'success');
            } else {
                this.showNotification(`Search failed: ${data.message}`, 'danger');
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showNotification('An error occurred during search.', 'danger');
        } finally {
            this.searchInProgress = false;
            this.hideSearchProgress();
        }
    },
    
    showSearchProgress() {
        const progressSection = document.getElementById('search-progress');
        progressSection.style.display = 'block';
        progressSection.classList.add('fade-in');
        
        const searchBtn = document.getElementById('search-btn');
        searchBtn.disabled = true;
        searchBtn.innerHTML = '<div class="loading-spinner"></div> Searching...';
        
        // Simulate progress updates
        let progress = 0;
        const progressBar = document.getElementById('search-progress-bar');
        const currentFileSpan = document.getElementById('current-file');
        const fileProgressSpan = document.getElementById('file-progress');
        const matchesFoundSpan = document.getElementById('matches-found');
        const searchStatusSpan = document.getElementById('search-status');
        
        this.progressInterval = setInterval(() => {
            if (!this.searchInProgress) {
                clearInterval(this.progressInterval);
                return;
            }
            
            progress += Math.random() * 10;
            if (progress > 95) progress = 95;
            
            progressBar.style.width = progress + '%';
            
            // Update status text
            const fileIndex = Math.floor((progress / 100) * this.selectedFiles.length);
            if (fileIndex < this.selectedFiles.length) {
                const fileName = this.selectedFiles[fileIndex].split('/').pop();
                currentFileSpan.textContent = fileName;
                fileProgressSpan.textContent = `${fileIndex + 1} / ${this.selectedFiles.length}`;
            }
            
            searchStatusSpan.textContent = 'Searching files...';
        }, 500);
    },
    
    hideSearchProgress() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }
        
        const searchBtn = document.getElementById('search-btn');
        searchBtn.disabled = this.selectedFiles.length === 0 || !document.getElementById('search-query').value.trim();
        searchBtn.innerHTML = '<i class="fas fa-search"></i> Search Files';
        
        // Complete progress bar
        const progressBar = document.getElementById('search-progress-bar');
        progressBar.style.width = '100%';
        document.getElementById('search-status').textContent = 'Search completed';
        
        setTimeout(() => {
            document.getElementById('search-progress').style.display = 'none';
        }, 2000);
    },
    
    viewFile(filePath) {
        window.open(`/open-document?path=${encodeURIComponent(filePath)}`, '_blank');
    },
    
    downloadFile(filePath) {
        window.open(`/download-document?path=${encodeURIComponent(filePath)}`, '_blank');
    },
    
    expandAllResults() {
        document.querySelectorAll('.result-body').forEach(body => {
            body.classList.add('expanded');
            body.style.display = 'block';
        });
    },
    
    collapseAllResults() {
        document.querySelectorAll('.result-body').forEach(body => {
            body.classList.remove('expanded');
            body.style.display = 'none';
        });
    }
});
</script>
