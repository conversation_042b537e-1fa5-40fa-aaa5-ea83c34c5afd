<script>
// Indexing v3.0 - Index Management Functions

Object.assign(IndexingApp.prototype, {
    
    async reindexFolder(folderPath) {
        if (confirm(`Are you sure you want to re-index the folder "${folderPath}"? This will update all files in the index.`)) {
            try {
                const response = await fetch('/reindex-folder', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ folder_path: folderPath })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    this.showNotification(`Started re-indexing folder: ${folderPath}`, 'success');
                    this.loadIndexedFolders();
                    this.updateStatistics();
                } else {
                    this.showNotification(`Failed to re-index folder: ${data.message}`, 'danger');
                }
            } catch (error) {
                console.error('Error re-indexing folder:', error);
                this.showNotification('Error re-indexing folder.', 'danger');
            }
        }
    },
    
    async removeFolderFromIndex(folderPath) {
        if (confirm(`Are you sure you want to remove "${folderPath}" from the index? This action cannot be undone.`)) {
            try {
                const response = await fetch('/remove-from-index', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ folder_path: folderPath })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    this.showNotification(`Removed folder from index: ${folderPath}`, 'success');
                    this.loadIndexedFolders();
                    this.updateStatistics();
                } else {
                    this.showNotification(`Failed to remove folder: ${data.message}`, 'danger');
                }
            } catch (error) {
                console.error('Error removing folder:', error);
                this.showNotification('Error removing folder from index.', 'danger');
            }
        }
    },
    
    async viewFolderDetails(folderPath) {
        try {
            const response = await fetch(`/folder-details?path=${encodeURIComponent(folderPath)}`);
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showFolderDetailsModal(data.details);
            } else {
                this.showNotification(`Failed to load folder details: ${data.message}`, 'danger');
            }
        } catch (error) {
            console.error('Error loading folder details:', error);
            this.showNotification('Error loading folder details.', 'danger');
        }
    },
    
    showFolderDetailsModal(details) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-backdrop" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 1000;"></div>
            <div class="modal-dialog" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 90%; max-width: 600px; z-index: 1001;">
                <div class="modal-content" style="background: white; border-radius: var(--border-radius); box-shadow: var(--shadow-lg); overflow: hidden;">
                    <div class="modal-header" style="padding: 1.5rem; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                        <h3 style="margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-info-circle" style="color: var(--primary-color);"></i>
                            Folder Details
                        </h3>
                        <button onclick="this.closest('.modal').remove(); document.body.style.overflow = 'auto';" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--secondary-color);">×</button>
                    </div>
                    <div class="modal-body" style="padding: 1.5rem;">
                        <div style="margin-bottom: 1rem;">
                            <h4 style="color: var(--primary-color); margin-bottom: 0.5rem;">Folder Information</h4>
                            <p><strong>Path:</strong> ${details.path || 'Unknown'}</p>
                            <p><strong>Total Files:</strong> ${details.total_files || 0}</p>
                            <p><strong>Indexed Files:</strong> ${details.indexed_files || 0}</p>
                            <p><strong>Last Indexed:</strong> ${details.last_indexed || 'Never'}</p>
                            <p><strong>Status:</strong> <span style="color: ${details.status === 'indexed' ? 'var(--success-color)' : 'var(--warning-color)'};">${details.status || 'Unknown'}</span></p>
                        </div>
                        
                        ${details.file_types ? `
                            <div style="margin-bottom: 1rem;">
                                <h4 style="color: var(--primary-color); margin-bottom: 0.5rem;">File Types</h4>
                                <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                                    ${Object.entries(details.file_types).map(([type, count]) => 
                                        `<span style="background: var(--light-color); padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.875rem;">
                                            ${type.toUpperCase()}: ${count}
                                        </span>`
                                    ).join('')}
                                </div>
                            </div>
                        ` : ''}
                        
                        ${details.recent_files ? `
                            <div>
                                <h4 style="color: var(--primary-color); margin-bottom: 0.5rem;">Recent Files</h4>
                                <div style="max-height: 200px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: var(--border-radius);">
                                    ${details.recent_files.map(file => 
                                        `<div style="padding: 0.75rem; border-bottom: 1px solid var(--border-color); display: flex; align-items: center; gap: 0.75rem;">
                                            <i class="${this.getFileIcon(file.extension)}" style="color: ${this.getFileIconColor(file.extension)};"></i>
                                            <div style="flex: 1;">
                                                <div style="font-weight: 500;">${file.name}</div>
                                                <div style="font-size: 0.75rem; color: var(--secondary-color);">${file.size || 'Unknown size'}</div>
                                            </div>
                                        </div>`
                                    ).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer" style="padding: 1.5rem; border-top: 1px solid var(--border-color); display: flex; justify-content: flex-end;">
                        <button onclick="this.closest('.modal').remove(); document.body.style.overflow = 'auto';" class="btn btn-secondary">Close</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        document.body.style.overflow = 'hidden';
        
        // Close on backdrop click
        modal.querySelector('.modal-backdrop').addEventListener('click', () => {
            modal.remove();
            document.body.style.overflow = 'auto';
        });
    },
    
    async reindexAllFolders() {
        if (this.indexedFolders.length === 0) {
            this.showNotification('No folders to re-index.', 'warning');
            return;
        }
        
        if (confirm(`Are you sure you want to re-index all ${this.indexedFolders.length} folders? This may take a while.`)) {
            try {
                const response = await fetch('/reindex-all', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    this.showNotification('Started re-indexing all folders.', 'success');
                    this.updateStatistics();
                } else {
                    this.showNotification(`Failed to start re-indexing: ${data.message}`, 'danger');
                }
            } catch (error) {
                console.error('Error re-indexing all folders:', error);
                this.showNotification('Error starting re-index operation.', 'danger');
            }
        }
    },
    
    async rebuildIndex() {
        if (confirm('Are you sure you want to rebuild the entire index? This will delete all existing index data and re-index all folders from scratch. This operation may take a long time.')) {
            try {
                const response = await fetch('/rebuild-index', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    this.showNotification('Started rebuilding index. This may take a while.', 'warning');
                    this.updateStatistics();
                } else {
                    this.showNotification(`Failed to rebuild index: ${data.message}`, 'danger');
                }
            } catch (error) {
                console.error('Error rebuilding index:', error);
                this.showNotification('Error rebuilding index.', 'danger');
            }
        }
    },
    
    async optimizeIndex() {
        try {
            const response = await fetch('/optimize-index', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showNotification('Index optimization completed.', 'success');
                this.updateStatistics();
            } else {
                this.showNotification(`Failed to optimize index: ${data.message}`, 'danger');
            }
        } catch (error) {
            console.error('Error optimizing index:', error);
            this.showNotification('Error optimizing index.', 'danger');
        }
    },
    
    async clearIndex() {
        if (confirm('Are you sure you want to clear the entire index? This will remove all indexed data and cannot be undone.')) {
            try {
                const response = await fetch('/clear-index', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    this.showNotification('Index cleared successfully.', 'success');
                    this.indexedFolders = [];
                    this.displayIndexedFolders([]);
                    this.updateFolderFilters([]);
                    this.updateStatistics();
                } else {
                    this.showNotification(`Failed to clear index: ${data.message}`, 'danger');
                }
            } catch (error) {
                console.error('Error clearing index:', error);
                this.showNotification('Error clearing index.', 'danger');
            }
        }
    },
    
    // File operations (reuse from DirectSearch)
    async viewFile(filePath) {
        try {
            const response = await fetch(`/open-document?path=${encodeURIComponent(filePath)}`);
            if (response.ok) {
                window.open(`/open-document?path=${encodeURIComponent(filePath)}`, '_blank');
            } else {
                this.showNotification('Unable to open file. File may not exist or be accessible.', 'warning');
            }
        } catch (error) {
            console.error('Error opening file:', error);
            this.showNotification('Error opening file.', 'danger');
        }
    },
    
    async downloadFile(filePath) {
        try {
            const link = document.createElement('a');
            link.href = `/download-document?path=${encodeURIComponent(filePath)}`;
            link.download = filePath.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.showNotification('Download started...', 'success');
        } catch (error) {
            console.error('Error downloading file:', error);
            this.showNotification('Error downloading file.', 'danger');
        }
    },
    
    toggleIndexedResultExpansion(button) {
        const resultCard = button.closest('.result-card');
        const resultBody = resultCard.querySelector('.result-body');
        const chevron = button.querySelector('i');
        
        if (resultBody.style.display === 'block') {
            resultBody.style.display = 'none';
            resultBody.classList.remove('expanded');
            chevron.style.transform = 'rotate(0deg)';
        } else {
            resultBody.style.display = 'block';
            resultBody.classList.add('expanded');
            chevron.style.transform = 'rotate(180deg)';
        }
    },
    
    expandAllIndexedResults() {
        document.querySelectorAll('#indexed-results-container .result-body').forEach(body => {
            body.classList.add('expanded');
            body.style.display = 'block';
        });
    },
    
    collapseAllIndexedResults() {
        document.querySelectorAll('#indexed-results-container .result-body').forEach(body => {
            body.classList.remove('expanded');
            body.style.display = 'none';
        });
    },
    
    applyIndexedDocumentTypeFilters() {
        // This method can be used to filter results by document type
        // Implementation depends on how the backend handles filtering
        console.log('Document type filters updated');
    }
});

// Add keyboard shortcuts for indexing page
document.addEventListener('keydown', (e) => {
    if (window.indexingApp && window.location.pathname.includes('indexing')) {
        // Ctrl+F or Cmd+F to focus search input
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            document.getElementById('indexed-search-query').focus();
        }
        
        // Escape to close modal
        if (e.key === 'Escape') {
            const modal = document.getElementById('folder-browser-modal');
            if (modal.style.display === 'block') {
                window.indexingApp.closeFolderModal();
            }
        }
        
        // Ctrl+R or Cmd+R to refresh folders
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            window.indexingApp.loadIndexedFolders();
        }
    }
});

// Auto-refresh statistics every 30 seconds
setInterval(() => {
    if (window.indexingApp) {
        window.indexingApp.updateStatistics();
    }
}, 30000);
</script>
